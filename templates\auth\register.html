{% extends "base.html" %}

{% block title %}Register - Digital Nashop{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="glass-card p-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Create Account</h2>
                <p class="text-gray-600">Join <PERSON> Nashop and start shopping</p>
            </div>

            <form method="POST" class="mt-8 space-y-6" id="register-form">
                {{ csrf_token() }}
                
                <div class="form-group">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" 
                           id="username" 
                           name="username" 
                           class="form-input" 
                           placeholder="Choose a username"
                           minlength="3"
                           required>
                    <div class="username-feedback text-sm mt-1" style="display: none;"></div>
                </div>

                <div class="form-group">
                    <label for="email" class="form-label">Email Address</label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           class="form-input" 
                           placeholder="Enter your email"
                           required>
                    <div class="email-feedback text-sm mt-1" style="display: none;"></div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="relative">
                        <input type="password" 
                               id="password" 
                               name="password" 
                               class="form-input pr-10" 
                               placeholder="Create a password"
                               minlength="6"
                               required>
                        <button type="button" 
                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                onclick="togglePassword('password')">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- Password Strength Indicator -->
                    <div class="password-strength mt-2" style="display: none;">
                        <div class="text-sm text-gray-600 mb-1">Password strength:</div>
                        <div class="flex space-x-1">
                            <div class="strength-bar h-2 bg-gray-200 rounded flex-1"></div>
                            <div class="strength-bar h-2 bg-gray-200 rounded flex-1"></div>
                            <div class="strength-bar h-2 bg-gray-200 rounded flex-1"></div>
                            <div class="strength-bar h-2 bg-gray-200 rounded flex-1"></div>
                        </div>
                        <div class="strength-text text-xs mt-1"></div>
                    </div>
                    
                    <!-- Password Requirements -->
                    <div class="password-requirements mt-2 text-xs text-gray-500">
                        <div class="requirement" data-requirement="length">
                            <span class="indicator">○</span> At least 6 characters
                        </div>
                        <div class="requirement" data-requirement="letter">
                            <span class="indicator">○</span> Contains a letter
                        </div>
                        <div class="requirement" data-requirement="number">
                            <span class="indicator">○</span> Contains a number
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirm_password" class="form-label">Confirm Password</label>
                    <div class="relative">
                        <input type="password" 
                               id="confirm_password" 
                               name="confirm_password" 
                               class="form-input pr-10" 
                               placeholder="Confirm your password"
                               required>
                        <button type="button" 
                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                onclick="togglePassword('confirm_password')">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="confirm-password-feedback text-sm mt-1" style="display: none;"></div>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" 
                           id="terms" 
                           name="terms" 
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                           required>
                    <label for="terms" class="ml-2 block text-sm text-gray-900">
                        I agree to the 
                        <a href="{{ url_for('main.terms') }}" class="text-blue-600 hover:text-blue-500" target="_blank">
                            Terms of Service
                        </a> 
                        and 
                        <a href="{{ url_for('main.privacy') }}" class="text-blue-600 hover:text-blue-500" target="_blank">
                            Privacy Policy
                        </a>
                    </label>
                </div>

                <div>
                    <button type="submit" class="btn btn-primary w-full" id="submit-btn">
                        Create Account
                    </button>
                </div>

                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Already have an account?
                        <a href="{{ url_for('auth.login') }}" class="font-medium text-blue-600 hover:text-blue-500">
                            Sign in here
                        </a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
    field.setAttribute('type', type);
}

// Real-time validation
document.addEventListener('DOMContentLoaded', function() {
    const usernameField = document.getElementById('username');
    const emailField = document.getElementById('email');
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');
    
    let usernameTimeout, emailTimeout;

    // Username validation
    usernameField.addEventListener('input', function() {
        clearTimeout(usernameTimeout);
        usernameTimeout = setTimeout(() => {
            checkUsername(this.value);
        }, 500);
    });

    // Email validation
    emailField.addEventListener('input', function() {
        clearTimeout(emailTimeout);
        emailTimeout = setTimeout(() => {
            checkEmail(this.value);
        }, 500);
    });

    // Password strength
    passwordField.addEventListener('input', function() {
        checkPasswordStrength(this.value);
        checkPasswordMatch();
    });

    // Confirm password
    confirmPasswordField.addEventListener('input', checkPasswordMatch);

    async function checkUsername(username) {
        if (username.length < 3) return;
        
        try {
            const response = await fetch(`/auth/check-username?username=${encodeURIComponent(username)}`);
            const data = await response.json();
            showFieldFeedback('username', data.message, data.available);
        } catch (error) {
            console.error('Username check failed:', error);
        }
    }

    async function checkEmail(email) {
        if (!email.includes('@')) return;
        
        try {
            const response = await fetch(`/auth/check-email?email=${encodeURIComponent(email)}`);
            const data = await response.json();
            showFieldFeedback('email', data.message, data.available);
        } catch (error) {
            console.error('Email check failed:', error);
        }
    }

    function checkPasswordStrength(password) {
        const requirements = {
            length: password.length >= 6,
            letter: /[a-zA-Z]/.test(password),
            number: /[0-9]/.test(password)
        };

        // Update requirement indicators
        Object.keys(requirements).forEach(req => {
            const element = document.querySelector(`[data-requirement="${req}"]`);
            const indicator = element.querySelector('.indicator');
            if (requirements[req]) {
                indicator.textContent = '✓';
                indicator.style.color = 'green';
            } else {
                indicator.textContent = '○';
                indicator.style.color = 'gray';
            }
        });

        // Show/hide strength indicator
        const strengthContainer = document.querySelector('.password-strength');
        if (password.length > 0) {
            strengthContainer.style.display = 'block';
            
            // Calculate strength
            const score = Object.values(requirements).filter(Boolean).length;
            const bars = document.querySelectorAll('.strength-bar');
            const strengthText = document.querySelector('.strength-text');
            
            bars.forEach((bar, index) => {
                if (index < score) {
                    bar.style.backgroundColor = score === 1 ? '#ef4444' : score === 2 ? '#f59e0b' : '#10b981';
                } else {
                    bar.style.backgroundColor = '#e5e7eb';
                }
            });
            
            const strengthLabels = ['Weak', 'Fair', 'Good', 'Strong'];
            strengthText.textContent = strengthLabels[score - 1] || 'Very Weak';
            strengthText.style.color = score === 1 ? '#ef4444' : score === 2 ? '#f59e0b' : '#10b981';
        } else {
            strengthContainer.style.display = 'none';
        }
    }

    function checkPasswordMatch() {
        const password = passwordField.value;
        const confirmPassword = confirmPasswordField.value;
        
        if (confirmPassword.length > 0) {
            const match = password === confirmPassword;
            showFieldFeedback('confirm_password', 
                match ? 'Passwords match' : 'Passwords do not match', 
                match);
        }
    }

    function showFieldFeedback(fieldName, message, isValid) {
        const field = document.getElementById(fieldName);
        const feedback = document.querySelector(`.${fieldName}-feedback`);
        
        if (feedback) {
            feedback.textContent = message;
            feedback.style.display = 'block';
            feedback.style.color = isValid ? 'green' : 'red';
            
            field.style.borderColor = isValid ? 'green' : 'red';
        }
    }
});
</script>
{% endblock %}
