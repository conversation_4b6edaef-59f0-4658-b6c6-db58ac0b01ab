// Digital Nashop - Main JavaScript

// Theme management
class ThemeManager {
    constructor() {
        this.theme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.applyTheme();
        this.createToggleButton();
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        localStorage.setItem('theme', this.theme);
    }

    toggle() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        this.updateToggleButton();
    }

    createToggleButton() {
        const toggle = document.createElement('button');
        toggle.className = 'theme-toggle';
        toggle.innerHTML = this.theme === 'light' ? '🌙' : '☀️';
        toggle.addEventListener('click', () => this.toggle());
        document.body.appendChild(toggle);
    }

    updateToggleButton() {
        const toggle = document.querySelector('.theme-toggle');
        if (toggle) {
            toggle.innerHTML = this.theme === 'light' ? '🌙' : '☀️';
        }
    }
}

// Toast notifications
class ToastManager {
    constructor() {
        this.container = this.createContainer();
    }

    createContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 350px;
        `;
        document.body.appendChild(container);
        return container;
    }

    show(message, type = 'info', duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type}`;
        toast.style.cssText = `
            margin-bottom: 10px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease-in-out;
        `;
        toast.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
            </div>
        `;

        this.container.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);

        // Auto remove
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => toast.remove(), 300);
            }
        }, duration);
    }
}

// Cart management
class CartManager {
    constructor() {
        this.updateCartCount();
    }

    async addToCart(productId, quantity = 1) {
        try {
            const formData = new FormData();
            formData.append('product_id', productId);
            formData.append('quantity', quantity);

            const response = await fetch('/add-to-cart', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                }
            });

            const data = await response.json();

            if (data.success) {
                toast.show(data.message, 'success');
                this.updateCartCount(data.cart_count);
            } else {
                toast.show(data.message, 'error');
            }
        } catch (error) {
            toast.show('Failed to add item to cart', 'error');
        }
    }

    async updateCartCount() {
        try {
            const response = await fetch('/cart-count');
            const data = await response.json();
            this.setCartCount(data.count);
        } catch (error) {
            console.error('Failed to update cart count:', error);
        }
    }

    setCartCount(count) {
        const badges = document.querySelectorAll('.cart-count');
        badges.forEach(badge => {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'inline' : 'none';
        });
    }

    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }
}

// Search functionality
class SearchManager {
    constructor() {
        this.searchInput = document.querySelector('#search-input');
        this.searchResults = document.querySelector('#search-results');
        this.debounceTimer = null;

        if (this.searchInput) {
            this.init();
        }
    }

    init() {
        this.searchInput.addEventListener('input', (e) => {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(() => {
                this.search(e.target.value);
            }, 300);
        });

        this.searchInput.addEventListener('focus', () => {
            if (this.searchResults) {
                this.searchResults.style.display = 'block';
            }
        });

        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                if (this.searchResults) {
                    this.searchResults.style.display = 'none';
                }
            }
        });
    }

    async search(query) {
        if (query.length < 2) {
            this.hideResults();
            return;
        }

        try {
            const response = await fetch(`/search?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            this.displayResults(data.products);
        } catch (error) {
            console.error('Search failed:', error);
        }
    }

    displayResults(products) {
        if (!this.searchResults) return;

        if (products.length === 0) {
            this.searchResults.innerHTML = '<div class="p-4 text-gray-500">No products found</div>';
        } else {
            this.searchResults.innerHTML = products.map(product => `
                <a href="/product/${product.id}" class="block p-3 hover:bg-gray-50 border-b">
                    <div class="flex items-center space-x-3">
                        <img src="${product.image_url || '/static/images/placeholder.jpg'}" 
                             alt="${product.name}" class="w-10 h-10 object-cover rounded">
                        <div>
                            <div class="font-medium">${product.name}</div>
                            <div class="text-sm text-gray-500">$${product.price.toFixed(2)}</div>
                        </div>
                    </div>
                </a>
            `).join('');
        }

        this.searchResults.style.display = 'block';
    }

    hideResults() {
        if (this.searchResults) {
            this.searchResults.style.display = 'none';
        }
    }
}

// Form validation
class FormValidator {
    static validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    static validatePassword(password) {
        return {
            length: password.length >= 6,
            letter: /[a-zA-Z]/.test(password),
            number: /[0-9]/.test(password)
        };
    }

    static showFieldError(field, message) {
        const errorElement = field.parentElement.querySelector('.field-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        } else {
            const error = document.createElement('div');
            error.className = 'field-error text-red-500 text-sm mt-1';
            error.textContent = message;
            field.parentElement.appendChild(error);
        }
        field.classList.add('border-red-500');
    }

    static clearFieldError(field) {
        const errorElement = field.parentElement.querySelector('.field-error');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
        field.classList.remove('border-red-500');
    }
}

// Loading states
class LoadingManager {
    static showButtonLoading(button, text = 'Loading...') {
        button.disabled = true;
        button.innerHTML = `<span class="spinner"></span> ${text}`;
    }

    static hideButtonLoading(button, originalText) {
        button.disabled = false;
        button.innerHTML = originalText;
    }

    static showPageLoading() {
        const loader = document.createElement('div');
        loader.id = 'page-loader';
        loader.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        `;
        loader.innerHTML = '<div class="spinner" style="width: 40px; height: 40px;"></div>';
        document.body.appendChild(loader);
    }

    static hidePageLoading() {
        const loader = document.getElementById('page-loader');
        if (loader) {
            loader.remove();
        }
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize managers
    window.themeManager = new ThemeManager();
    window.toast = new ToastManager();
    window.cart = new CartManager();
    window.search = new SearchManager();

    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Auto-hide alerts after 5 seconds
    document.querySelectorAll('.alert').forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-10px)';
            setTimeout(() => alert.remove(), 300);
        }, 5000);
    });

    // Add loading states to forms
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                LoadingManager.showButtonLoading(submitButton);
            }
        });
    });
});
