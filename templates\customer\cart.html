{% extends "base.html" %}

{% block title %}Shopping Cart - Digital Nashop{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center justify-between mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Shopping Cart</h1>
        <a href="{{ url_for('main.products') }}" class="btn btn-secondary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Continue Shopping
        </a>
    </div>

    {% if cart_products %}
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Cart Items -->
        <div class="lg:col-span-2">
            <div class="glass-card p-6">
                <h2 class="text-xl font-semibold mb-6">Cart Items ({{ cart_products|length }})</h2>
                
                <div class="space-y-6">
                    {% for item in cart_products %}
                    <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                        <!-- Product Image -->
                        <div class="flex-shrink-0">
                            <img src="{{ item.product.image_url or '/static/images/placeholder.jpg' }}" 
                                 alt="{{ item.product.name }}" 
                                 class="w-20 h-20 object-cover rounded-lg">
                        </div>
                        
                        <!-- Product Details -->
                        <div class="flex-1 min-w-0">
                            <h3 class="text-lg font-semibold text-gray-900 mb-1">
                                <a href="{{ url_for('main.product_detail', product_id=item.product.id) }}" 
                                   class="hover:text-blue-600">
                                    {{ item.product.name }}
                                </a>
                            </h3>
                            <p class="text-sm text-gray-600 mb-2">
                                {{ item.product.category.replace('_', ' ').title() }}
                            </p>
                            <p class="text-lg font-bold text-blue-600">
                                ${{ "%.2f"|format(item.product.price) }} each
                            </p>
                        </div>
                        
                        <!-- Quantity Controls -->
                        <div class="flex items-center space-x-3">
                            <form method="POST" action="{{ url_for('customer.update_cart') }}" class="flex items-center space-x-2">
                                {{ csrf_token() }}
                                <input type="hidden" name="product_id" value="{{ item.product.id }}">
                                
                                <button type="button" 
                                        onclick="changeCartQuantity({{ item.product.id }}, -1, {{ item.product.stock }})"
                                        class="btn btn-secondary btn-sm w-8 h-8 flex items-center justify-center p-0">
                                    -
                                </button>
                                
                                <input type="number" 
                                       id="quantity-{{ item.product.id }}"
                                       name="quantity" 
                                       value="{{ item.quantity }}" 
                                       min="1" 
                                       max="{{ item.product.stock }}"
                                       class="form-input text-center w-16 h-8 text-sm"
                                       onchange="updateCartItem({{ item.product.id }})">
                                
                                <button type="button" 
                                        onclick="changeCartQuantity({{ item.product.id }}, 1, {{ item.product.stock }})"
                                        class="btn btn-secondary btn-sm w-8 h-8 flex items-center justify-center p-0">
                                    +
                                </button>
                            </form>
                        </div>
                        
                        <!-- Item Total -->
                        <div class="text-right">
                            <p class="text-lg font-bold text-gray-900">
                                ${{ "%.2f"|format(item.total) }}
                            </p>
                            <form method="POST" action="{{ url_for('customer.update_cart') }}" class="mt-2">
                                {{ csrf_token() }}
                                <input type="hidden" name="product_id" value="{{ item.product.id }}">
                                <input type="hidden" name="quantity" value="0">
                                <button type="submit" 
                                        class="text-red-600 hover:text-red-800 text-sm"
                                        onclick="return confirm('Remove this item from cart?')">
                                    Remove
                                </button>
                            </form>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
            <div class="glass-card p-6 sticky top-24">
                <h2 class="text-xl font-semibold mb-6">Order Summary</h2>
                
                <div class="space-y-4 mb-6">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Subtotal</span>
                        <span class="font-semibold">${{ "%.2f"|format(total) }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Processing Fee</span>
                        <span class="font-semibold">$0.00</span>
                    </div>
                    
                    <div class="border-t pt-4">
                        <div class="flex justify-between text-lg font-bold">
                            <span>Total</span>
                            <span class="text-blue-600">${{ "%.2f"|format(total) }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Wallet Balance Info -->
                <div class="bg-blue-50 p-4 rounded-lg mb-6">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600">Wallet Balance</span>
                        <span class="font-semibold text-green-600">
                            ${{ "%.2f"|format(current_user.balance) }}
                        </span>
                    </div>
                    {% if current_user.balance < total %}
                        <p class="text-sm text-red-600">
                            Insufficient balance. 
                            <a href="{{ url_for('customer.wallet') }}" class="underline">Add funds</a>
                        </p>
                    {% else %}
                        <p class="text-sm text-green-600">
                            ✓ Sufficient balance for this order
                        </p>
                    {% endif %}
                </div>
                
                <!-- Checkout Button -->
                <a href="{{ url_for('customer.checkout') }}" 
                   class="btn btn-primary w-full text-center mb-4">
                    Proceed to Checkout
                </a>
                
                <!-- Security Info -->
                <div class="text-center text-sm text-gray-500">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    Secure checkout with instant delivery
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Empty Cart -->
    <div class="glass-card p-12 text-center">
        <svg class="w-24 h-24 mx-auto text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
        </svg>
        
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
        <p class="text-gray-600 mb-8 max-w-md mx-auto">
            Looks like you haven't added any products to your cart yet. Start shopping to fill it up!
        </p>
        
        <div class="space-y-4">
            <a href="{{ url_for('main.products') }}" class="btn btn-primary btn-lg">
                Browse All Products
            </a>
            
            <div class="flex justify-center space-x-4 text-sm">
                <a href="{{ url_for('main.products', category='giftcard') }}" 
                   class="text-blue-600 hover:text-blue-800">Gift Cards</a>
                <a href="{{ url_for('main.products', category='followers') }}" 
                   class="text-blue-600 hover:text-blue-800">Social Media</a>
                <a href="{{ url_for('main.products', category='gaming_tokens') }}" 
                   class="text-blue-600 hover:text-blue-800">Gaming Tokens</a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function changeCartQuantity(productId, delta, maxStock) {
    const quantityInput = document.getElementById(`quantity-${productId}`);
    let newQuantity = parseInt(quantityInput.value) + delta;
    
    if (newQuantity < 1) newQuantity = 1;
    if (newQuantity > maxStock) newQuantity = maxStock;
    
    quantityInput.value = newQuantity;
    updateCartItem(productId);
}

function updateCartItem(productId) {
    const quantityInput = document.getElementById(`quantity-${productId}`);
    const quantity = parseInt(quantityInput.value);
    
    // Create and submit form
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ url_for("customer.update_cart") }}';
    
    // Add CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = csrfToken;
    form.appendChild(csrfInput);
    
    // Add product ID
    const productInput = document.createElement('input');
    productInput.type = 'hidden';
    productInput.name = 'product_id';
    productInput.value = productId;
    form.appendChild(productInput);
    
    // Add quantity
    const quantityInputHidden = document.createElement('input');
    quantityInputHidden.type = 'hidden';
    quantityInputHidden.name = 'quantity';
    quantityInputHidden.value = quantity;
    form.appendChild(quantityInputHidden);
    
    document.body.appendChild(form);
    form.submit();
}

// Auto-update cart when quantity changes
document.addEventListener('DOMContentLoaded', function() {
    const quantityInputs = document.querySelectorAll('input[type="number"][name="quantity"]');
    quantityInputs.forEach(input => {
        let timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                const productId = this.closest('form').querySelector('input[name="product_id"]').value;
                updateCartItem(productId);
            }, 1000); // Wait 1 second after user stops typing
        });
    });
});
</script>
{% endblock %}
