#!/usr/bin/env python3
"""
Sample Data Population Script for Digital Nashop
This script populates the database with sample products, users, and transactions for testing.
"""

from app import app
from models import db, User, Product, Order, Transaction
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import random

def create_sample_users():
    """Create sample users"""
    print("Creating sample users...")
    
    # Sample customers
    customers = [
        {'username': 'john_doe', 'email': '<EMAIL>', 'balance': 150.00},
        {'username': 'jane_smith', 'email': '<EMAIL>', 'balance': 75.50},
        {'username': 'mike_wilson', 'email': '<EMAIL>', 'balance': 200.00},
        {'username': 'sarah_jones', 'email': '<EMAIL>', 'balance': 50.25},
        {'username': 'alex_brown', 'email': '<EMAIL>', 'balance': 300.00},
    ]
    
    for customer_data in customers:
        # Check if user already exists
        existing_user = User.query.filter_by(username=customer_data['username']).first()
        if not existing_user:
            user = User(
                username=customer_data['username'],
                email=customer_data['email'],
                password_hash=generate_password_hash('password123'),
                role='user',
                balance=customer_data['balance']
            )
            db.session.add(user)
    
    db.session.commit()
    print(f"Created {len(customers)} sample customers")

def create_sample_products():
    """Create sample products"""
    print("Creating sample products...")
    
    # Sample products
    products = [
        # Gift Cards
        {
            'name': 'Amazon Gift Card $25',
            'category': 'giftcard',
            'description': 'Digital Amazon gift card with $25 value. Instant delivery to your email.',
            'price': 25.00,
            'stock': 100,
            'image_url': 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400'
        },
        {
            'name': 'Steam Wallet Code $50',
            'category': 'giftcard',
            'description': 'Steam digital wallet code worth $50. Perfect for gaming enthusiasts.',
            'price': 50.00,
            'stock': 75,
            'image_url': 'https://images.unsplash.com/photo-1550745165-9bc0b252726f?w=400'
        },
        {
            'name': 'iTunes Gift Card $10',
            'category': 'giftcard',
            'description': 'Apple iTunes gift card for music, apps, and more. $10 value.',
            'price': 10.00,
            'stock': 150,
            'image_url': 'https://images.unsplash.com/photo-1611532736597-de2d4265fba3?w=400'
        },
        {
            'name': 'Google Play Gift Card $15',
            'category': 'giftcard',
            'description': 'Google Play Store gift card for Android apps and games. $15 value.',
            'price': 15.00,
            'stock': 120,
            'image_url': 'https://images.unsplash.com/photo-*************-f7fd0460ccdb?w=400'
        },
        
        # Social Media Followers
        {
            'name': 'Instagram Followers - 1000',
            'category': 'followers',
            'description': 'High-quality Instagram followers to boost your social presence. Real accounts, gradual delivery.',
            'price': 12.99,
            'stock': 50,
            'image_url': 'https://images.unsplash.com/photo-*************-d12430b98920?w=400'
        },
        {
            'name': 'TikTok Followers - 500',
            'category': 'followers',
            'description': 'Authentic TikTok followers to increase your reach and engagement.',
            'price': 8.99,
            'stock': 75,
            'image_url': 'https://images.unsplash.com/photo-*************-8b1569810432?w=400'
        },
        {
            'name': 'YouTube Subscribers - 250',
            'category': 'followers',
            'description': 'Real YouTube subscribers to grow your channel organically.',
            'price': 15.99,
            'stock': 30,
            'image_url': 'https://images.unsplash.com/photo-*************-5b21e879e113?w=400'
        },
        {
            'name': 'Twitter Followers - 2000',
            'category': 'followers',
            'description': 'Premium Twitter followers to expand your social network.',
            'price': 19.99,
            'stock': 40,
            'image_url': 'https://images.unsplash.com/photo-1611605698323-b1e99cfd37ea?w=400'
        },
        
        # Gaming Tokens
        {
            'name': 'Fortnite V-Bucks - 1000',
            'category': 'gaming_tokens',
            'description': '1000 Fortnite V-Bucks for skins, emotes, and battle passes.',
            'price': 9.99,
            'stock': 200,
            'image_url': 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400'
        },
        {
            'name': 'Roblox Robux - 800',
            'category': 'gaming_tokens',
            'description': '800 Robux for Roblox games and accessories. Instant delivery.',
            'price': 9.95,
            'stock': 150,
            'image_url': 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400'
        },
        {
            'name': 'Minecraft Minecoins - 1720',
            'category': 'gaming_tokens',
            'description': '1720 Minecoins for Minecraft Marketplace content.',
            'price': 9.99,
            'stock': 100,
            'image_url': 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400'
        },
        {
            'name': 'League of Legends RP - 1380',
            'category': 'gaming_tokens',
            'description': '1380 Riot Points for League of Legends champions and skins.',
            'price': 10.00,
            'stock': 80,
            'image_url': 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400'
        },
    ]
    
    for product_data in products:
        # Check if product already exists
        existing_product = Product.query.filter_by(name=product_data['name']).first()
        if not existing_product:
            product = Product(
                name=product_data['name'],
                category=product_data['category'],
                description=product_data['description'],
                price=product_data['price'],
                stock=product_data['stock'],
                image_url=product_data['image_url'],
                is_active=True
            )
            db.session.add(product)
    
    db.session.commit()
    print(f"Created {len(products)} sample products")

def create_sample_orders_and_transactions():
    """Create sample orders and transactions"""
    print("Creating sample orders and transactions...")
    
    users = User.query.filter_by(role='user').all()
    products = Product.query.all()
    
    if not users or not products:
        print("No users or products found. Skipping order creation.")
        return
    
    # Create some sample orders
    orders_created = 0
    transactions_created = 0
    
    for _ in range(20):  # Create 20 random orders
        user = random.choice(users)
        product = random.choice(products)
        quantity = random.randint(1, 3)
        total_price = float(product.price) * quantity
        
        # Random order status
        statuses = ['pending', 'processing', 'delivered', 'cancelled']
        weights = [0.2, 0.3, 0.4, 0.1]  # More delivered orders
        status = random.choices(statuses, weights=weights)[0]
        
        # Random date within last 30 days
        days_ago = random.randint(0, 30)
        created_at = datetime.utcnow() - timedelta(days=days_ago)
        
        order = Order(
            user_id=user.id,
            product_id=product.id,
            quantity=quantity,
            total_price=total_price,
            status=status,
            created_at=created_at
        )
        db.session.add(order)
        db.session.flush()  # Get order ID
        orders_created += 1
        
        # Create corresponding transaction
        if status in ['processing', 'delivered']:
            transaction = Transaction(
                user_id=user.id,
                order_id=order.id,
                type='purchase',
                amount=total_price,
                description=f"Purchase of {product.name}",
                created_at=created_at
            )
            db.session.add(transaction)
            transactions_created += 1
        elif status == 'cancelled':
            # Create both purchase and refund transactions
            purchase_transaction = Transaction(
                user_id=user.id,
                order_id=order.id,
                type='purchase',
                amount=total_price,
                description=f"Purchase of {product.name}",
                created_at=created_at
            )
            db.session.add(purchase_transaction)
            
            refund_transaction = Transaction(
                user_id=user.id,
                order_id=order.id,
                type='refund',
                amount=total_price,
                description=f"Refund for cancelled order #{order.id}",
                created_at=created_at + timedelta(hours=1)
            )
            db.session.add(refund_transaction)
            transactions_created += 2
    
    # Create some wallet credit transactions
    for user in users[:3]:  # First 3 users get wallet credits
        for _ in range(random.randint(1, 3)):
            amount = random.choice([25.00, 50.00, 100.00])
            days_ago = random.randint(0, 30)
            created_at = datetime.utcnow() - timedelta(days=days_ago)
            
            transaction = Transaction(
                user_id=user.id,
                type='wallet_credit',
                amount=amount,
                description='Wallet credit (simulated payment)',
                created_at=created_at
            )
            db.session.add(transaction)
            transactions_created += 1
    
    db.session.commit()
    print(f"Created {orders_created} sample orders and {transactions_created} transactions")

def main():
    """Main function to populate sample data"""
    print("Starting sample data population...")
    
    with app.app_context():
        # Create sample data
        create_sample_users()
        create_sample_products()
        create_sample_orders_and_transactions()
        
        print("\nSample data population completed!")
        print("\nSummary:")
        print(f"- Users: {User.query.count()}")
        print(f"- Products: {Product.query.count()}")
        print(f"- Orders: {Order.query.count()}")
        print(f"- Transactions: {Transaction.query.count()}")
        
        print("\nDefault admin account:")
        print("Username: admin")
        print("Password: admin123")
        
        print("\nSample customer accounts:")
        customers = User.query.filter_by(role='user').all()
        for customer in customers[:3]:
            print(f"Username: {customer.username}, Password: password123")

if __name__ == '__main__':
    main()
