{% extends "base.html" %}

{% block title %}Order History - Digital Nashop{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center justify-between mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Order History</h1>
        <a href="{{ url_for('main.products') }}" class="btn btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            Shop More
        </a>
    </div>

    <!-- Filter Options -->
    <div class="glass-card p-4 mb-6">
        <div class="flex flex-wrap items-center gap-4">
            <span class="text-sm font-medium text-gray-700">Filter by status:</span>
            
            <a href="{{ url_for('customer.orders') }}" 
               class="px-3 py-1 rounded-full text-sm font-medium transition-colors
                      {% if not status_filter %}bg-blue-100 text-blue-800{% else %}bg-gray-100 text-gray-600 hover:bg-gray-200{% endif %}">
                All Orders
            </a>
            
            <a href="{{ url_for('customer.orders', status='pending') }}" 
               class="px-3 py-1 rounded-full text-sm font-medium transition-colors
                      {% if status_filter == 'pending' %}bg-yellow-100 text-yellow-800{% else %}bg-gray-100 text-gray-600 hover:bg-gray-200{% endif %}">
                Pending
            </a>
            
            <a href="{{ url_for('customer.orders', status='processing') }}" 
               class="px-3 py-1 rounded-full text-sm font-medium transition-colors
                      {% if status_filter == 'processing' %}bg-blue-100 text-blue-800{% else %}bg-gray-100 text-gray-600 hover:bg-gray-200{% endif %}">
                Processing
            </a>
            
            <a href="{{ url_for('customer.orders', status='delivered') }}" 
               class="px-3 py-1 rounded-full text-sm font-medium transition-colors
                      {% if status_filter == 'delivered' %}bg-green-100 text-green-800{% else %}bg-gray-100 text-gray-600 hover:bg-gray-200{% endif %}">
                Delivered
            </a>
            
            <a href="{{ url_for('customer.orders', status='cancelled') }}" 
               class="px-3 py-1 rounded-full text-sm font-medium transition-colors
                      {% if status_filter == 'cancelled' %}bg-red-100 text-red-800{% else %}bg-gray-100 text-gray-600 hover:bg-gray-200{% endif %}">
                Cancelled
            </a>
        </div>
    </div>

    {% if orders.items %}
        <!-- Orders List -->
        <div class="space-y-6">
            {% for order in orders.items %}
            <div class="glass-card p-6">
                <div class="flex items-start justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">
                            Order #{{ order.id }}
                        </h3>
                        <p class="text-sm text-gray-600">
                            Placed on {{ order.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                        </p>
                        {% if order.updated_at != order.created_at %}
                            <p class="text-xs text-gray-500">
                                Last updated: {{ order.updated_at.strftime('%B %d, %Y at %I:%M %p') }}
                            </p>
                        {% endif %}
                    </div>
                    
                    <div class="text-right">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                            {% elif order.status == 'processing' %}bg-blue-100 text-blue-800
                            {% elif order.status == 'delivered' %}bg-green-100 text-green-800
                            {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                            {% endif %}">
                            {% if order.status == 'pending' %}
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            {% elif order.status == 'processing' %}
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            {% elif order.status == 'delivered' %}
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            {% elif order.status == 'cancelled' %}
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            {% endif %}
                            {{ order.status.title() }}
                        </span>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Product Image -->
                    <div class="flex-shrink-0">
                        <img src="{{ order.product.image_url or '/static/images/placeholder.jpg' }}" 
                             alt="{{ order.product.name }}" 
                             class="w-16 h-16 object-cover rounded-lg">
                    </div>
                    
                    <!-- Product Details -->
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-1">
                            <a href="{{ url_for('main.product_detail', product_id=order.product.id) }}" 
                               class="hover:text-blue-600">
                                {{ order.product.name }}
                            </a>
                        </h4>
                        <p class="text-sm text-gray-600 mb-1">
                            {{ order.product.category.replace('_', ' ').title() }}
                        </p>
                        <div class="flex items-center space-x-4 text-sm text-gray-600">
                            <span>Quantity: {{ order.quantity }}</span>
                            <span>•</span>
                            <span>Unit Price: ${{ "%.2f"|format(order.product.price) }}</span>
                        </div>
                    </div>
                    
                    <!-- Order Total -->
                    <div class="text-right">
                        <p class="text-lg font-bold text-gray-900">
                            ${{ "%.2f"|format(order.total_price) }}
                        </p>
                        
                        <!-- Action Buttons -->
                        <div class="mt-2 space-y-1">
                            {% if order.status == 'delivered' %}
                                <button onclick="downloadProduct({{ order.id }})" 
                                        class="btn btn-primary btn-sm w-full">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Download
                                </button>
                            {% elif order.status == 'pending' %}
                                <button onclick="cancelOrder({{ order.id }})" 
                                        class="btn btn-secondary btn-sm w-full text-red-600 hover:text-red-800">
                                    Cancel Order
                                </button>
                            {% endif %}
                            
                            <a href="{{ url_for('main.product_detail', product_id=order.product.id) }}" 
                               class="btn btn-secondary btn-sm w-full">
                                Buy Again
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Order Status Timeline -->
                {% if order.status in ['processing', 'delivered'] %}
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex items-center justify-between text-sm">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="text-gray-600">Order Placed</span>
                        </div>
                        
                        {% if order.status in ['processing', 'delivered'] %}
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="text-gray-600">Processing</span>
                        </div>
                        {% endif %}
                        
                        {% if order.status == 'delivered' %}
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span class="text-gray-600">Delivered</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if orders.pages > 1 %}
        <div class="flex justify-center mt-8">
            <nav class="flex items-center space-x-2">
                {% if orders.has_prev %}
                    <a href="{{ url_for('customer.orders', page=orders.prev_num, status=status_filter) }}" 
                       class="btn btn-secondary btn-sm">
                        Previous
                    </a>
                {% endif %}
                
                {% for page_num in orders.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != orders.page %}
                            <a href="{{ url_for('customer.orders', page=page_num, status=status_filter) }}" 
                               class="btn btn-secondary btn-sm">
                                {{ page_num }}
                            </a>
                        {% else %}
                            <span class="btn btn-primary btn-sm">{{ page_num }}</span>
                        {% endif %}
                    {% else %}
                        <span class="px-3 py-1">…</span>
                    {% endif %}
                {% endfor %}
                
                {% if orders.has_next %}
                    <a href="{{ url_for('customer.orders', page=orders.next_num, status=status_filter) }}" 
                       class="btn btn-secondary btn-sm">
                        Next
                    </a>
                {% endif %}
            </nav>
        </div>
        {% endif %}
    {% else %}
        <!-- No Orders -->
        <div class="glass-card p-12 text-center">
            <svg class="w-24 h-24 mx-auto text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            
            <h2 class="text-2xl font-bold text-gray-900 mb-4">
                {% if status_filter %}
                    No {{ status_filter }} orders found
                {% else %}
                    No orders yet
                {% endif %}
            </h2>
            
            <p class="text-gray-600 mb-8 max-w-md mx-auto">
                {% if status_filter %}
                    You don't have any {{ status_filter }} orders at the moment.
                {% else %}
                    You haven't placed any orders yet. Start shopping to see your order history here!
                {% endif %}
            </p>
            
            <div class="space-y-4">
                <a href="{{ url_for('main.products') }}" class="btn btn-primary btn-lg">
                    Start Shopping
                </a>
                
                {% if status_filter %}
                <div>
                    <a href="{{ url_for('customer.orders') }}" class="text-blue-600 hover:text-blue-800">
                        View All Orders
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
function downloadProduct(orderId) {
    // Simulate download functionality
    toast.show('Download started! Check your downloads folder.', 'success');
    
    // In a real application, this would trigger an actual download
    // For demo purposes, we'll just show a success message
    setTimeout(() => {
        toast.show('Product downloaded successfully!', 'success');
    }, 2000);
}

function cancelOrder(orderId) {
    if (confirm('Are you sure you want to cancel this order? This action cannot be undone.')) {
        // Create and submit a form to cancel the order
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/orders/${orderId}/update-status`;
        
        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        
        // Add status
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = 'cancelled';
        form.appendChild(statusInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
