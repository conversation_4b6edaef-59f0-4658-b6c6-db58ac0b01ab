{% extends "base.html" %}

{% block title %}{{ product.name }} - Digital Nashop{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{{ url_for('main.index') }}" class="text-gray-700 hover:text-blue-600">Home</a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ url_for('main.products') }}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">Products</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ url_for('main.products', category=product.category) }}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">
                        {{ product.category.replace('_', ' ').title() }}
                    </a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-gray-500 md:ml-2">{{ product.name }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Product Image -->
        <div class="space-y-4">
            <div class="glass-card overflow-hidden">
                <img src="{{ product.image_url or '/static/images/placeholder.jpg' }}" 
                     alt="{{ product.name }}" 
                     class="w-full h-96 object-cover cursor-pointer"
                     onclick="openImageModal(this.src)">
            </div>
            
            <!-- Additional product images would go here -->
            <div class="grid grid-cols-4 gap-2">
                <!-- Placeholder for additional images -->
                <div class="glass-card p-2">
                    <img src="{{ product.image_url or '/static/images/placeholder.jpg' }}" 
                         alt="{{ product.name }}" 
                         class="w-full h-20 object-cover rounded cursor-pointer opacity-60 hover:opacity-100"
                         onclick="openImageModal(this.src)">
                </div>
            </div>
        </div>

        <!-- Product Details -->
        <div class="space-y-6">
            <div>
                <div class="flex items-center space-x-2 mb-2">
                    <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">
                        {{ product.category.replace('_', ' ').title() }}
                    </span>
                    {% if product.stock > 0 %}
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">
                            In Stock
                        </span>
                    {% else %}
                        <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-semibold">
                            Out of Stock
                        </span>
                    {% endif %}
                </div>
                
                <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ product.name }}</h1>
                
                <div class="flex items-center space-x-4 mb-6">
                    <span class="text-4xl font-bold text-blue-600">${{ "%.2f"|format(product.price) }}</span>
                    {% if product.stock > 0 %}
                        <span class="text-gray-600">{{ product.stock }} available</span>
                    {% endif %}
                </div>
            </div>

            <!-- Product Description -->
            <div class="glass-card p-6">
                <h3 class="text-lg font-semibold mb-3">Product Description</h3>
                <p class="text-gray-700 leading-relaxed">
                    {{ product.description or 'Premium digital product with instant delivery. Get immediate access to your purchase upon successful payment completion.' }}
                </p>
            </div>

            <!-- Purchase Options -->
            {% if current_user.is_authenticated %}
                {% if product.stock > 0 %}
                    <div class="glass-card p-6">
                        <h3 class="text-lg font-semibold mb-4">Purchase Options</h3>
                        
                        <form id="purchase-form" class="space-y-4">
                            <div>
                                <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">
                                    Quantity
                                </label>
                                <div class="flex items-center space-x-3">
                                    <button type="button" onclick="changeQuantity(-1)" 
                                            class="btn btn-secondary btn-sm w-10 h-10 flex items-center justify-center">
                                        -
                                    </button>
                                    <input type="number" 
                                           id="quantity" 
                                           name="quantity" 
                                           value="1" 
                                           min="1" 
                                           max="{{ product.stock }}"
                                           class="form-input text-center w-20">
                                    <button type="button" onclick="changeQuantity(1)" 
                                            class="btn btn-secondary btn-sm w-10 h-10 flex items-center justify-center">
                                        +
                                    </button>
                                </div>
                            </div>
                            
                            <div class="border-t pt-4">
                                <div class="flex justify-between items-center mb-4">
                                    <span class="text-lg font-semibold">Total:</span>
                                    <span id="total-price" class="text-2xl font-bold text-blue-600">
                                        ${{ "%.2f"|format(product.price) }}
                                    </span>
                                </div>
                                
                                <div class="space-y-3">
                                    <button type="button" 
                                            onclick="cart.addToCart({{ product.id }}, parseInt(document.getElementById('quantity').value))"
                                            class="btn btn-secondary w-full">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
                                        </svg>
                                        Add to Cart
                                    </button>
                                    
                                    <button type="button" 
                                            onclick="buyNow()"
                                            class="btn btn-primary w-full">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                        Buy Now
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                {% else %}
                    <div class="glass-card p-6 text-center">
                        <svg class="w-16 h-16 mx-auto text-red-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Out of Stock</h3>
                        <p class="text-gray-600">This product is currently unavailable. Check back later!</p>
                    </div>
                {% endif %}
            {% else %}
                <div class="glass-card p-6 text-center">
                    <svg class="w-16 h-16 mx-auto text-blue-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Login Required</h3>
                    <p class="text-gray-600 mb-4">Please log in to purchase this product</p>
                    <div class="space-y-2">
                        <a href="{{ url_for('auth.login') }}" class="btn btn-primary w-full">
                            Login
                        </a>
                        <a href="{{ url_for('auth.register') }}" class="btn btn-secondary w-full">
                            Create Account
                        </a>
                    </div>
                </div>
            {% endif %}

            <!-- Product Features -->
            <div class="glass-card p-6">
                <h3 class="text-lg font-semibold mb-4">Features</h3>
                <ul class="space-y-2">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Instant delivery
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        100% authentic
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        24/7 customer support
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Secure payment processing
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Related Products -->
    {% if related_products %}
    <div class="mt-16">
        <h2 class="text-3xl font-bold text-center mb-12">Related Products</h2>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {% for related_product in related_products %}
            <div class="product-card">
                <div class="relative">
                    <img src="{{ related_product.image_url or '/static/images/placeholder.jpg' }}" 
                         alt="{{ related_product.name }}" 
                         class="product-image">
                    <div class="absolute top-4 right-4">
                        {% if related_product.stock > 0 %}
                            <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                In Stock
                            </span>
                        {% else %}
                            <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                Out of Stock
                            </span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="p-4">
                    <h3 class="font-bold mb-2">{{ related_product.name }}</h3>
                    <p class="text-blue-600 font-bold">${{ "%.2f"|format(related_product.price) }}</p>
                    <a href="{{ url_for('main.product_detail', product_id=related_product.id) }}" 
                       class="btn btn-secondary btn-sm w-full mt-3">
                        View Details
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Image Modal -->
<div id="image-modal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden">
    <div class="relative max-w-4xl max-h-full p-4">
        <img id="modal-image" src="" alt="" class="max-w-full max-h-full object-contain">
        <button onclick="closeImageModal()" 
                class="absolute top-4 right-4 text-white text-2xl bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center">
            ×
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
const productPrice = {{ product.price }};
const maxStock = {{ product.stock }};

function changeQuantity(delta) {
    const quantityInput = document.getElementById('quantity');
    let newQuantity = parseInt(quantityInput.value) + delta;
    
    if (newQuantity < 1) newQuantity = 1;
    if (newQuantity > maxStock) newQuantity = maxStock;
    
    quantityInput.value = newQuantity;
    updateTotalPrice();
}

function updateTotalPrice() {
    const quantity = parseInt(document.getElementById('quantity').value);
    const total = productPrice * quantity;
    document.getElementById('total-price').textContent = '$' + total.toFixed(2);
}

function buyNow() {
    const quantity = parseInt(document.getElementById('quantity').value);
    // Add to cart and redirect to checkout
    cart.addToCart({{ product.id }}, quantity).then(() => {
        window.location.href = '/checkout';
    });
}

function openImageModal(src) {
    document.getElementById('modal-image').src = src;
    document.getElementById('image-modal').classList.remove('hidden');
}

function closeImageModal() {
    document.getElementById('image-modal').classList.add('hidden');
}

// Update total price when quantity changes
document.getElementById('quantity').addEventListener('input', updateTotalPrice);

// Close modal when clicking outside
document.getElementById('image-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});
</script>
{% endblock %}
