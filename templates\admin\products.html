{% extends "base.html" %}

{% block title %}Product Management - Digital Nashop Admin{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Product Management</h1>
            <p class="text-gray-600">Manage your digital products inventory</p>
        </div>
        <a href="{{ url_for('admin.add_product') }}" class="btn btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add New Product
        </a>
    </div>

    <!-- Filters -->
    <div class="glass-card p-4 mb-6">
        <div class="flex flex-wrap items-center gap-4">
            <!-- Search -->
            <div class="flex-1 min-w-64">
                <div class="relative">
                    <input type="text" 
                           id="search-input"
                           class="form-input pr-10" 
                           placeholder="Search products..."
                           value="{{ current_search }}">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Category Filter -->
            <select id="category-filter" class="form-select">
                <option value="">All Categories</option>
                <option value="giftcard" {% if current_category == 'giftcard' %}selected{% endif %}>Gift Cards</option>
                <option value="followers" {% if current_category == 'followers' %}selected{% endif %}>Social Media</option>
                <option value="gaming_tokens" {% if current_category == 'gaming_tokens' %}selected{% endif %}>Gaming Tokens</option>
            </select>

            <!-- Status Filter -->
            <select id="status-filter" class="form-select">
                <option value="">All Status</option>
                <option value="active" {% if current_status == 'active' %}selected{% endif %}>Active</option>
                <option value="inactive" {% if current_status == 'inactive' %}selected{% endif %}>Inactive</option>
                <option value="low_stock" {% if current_status == 'low_stock' %}selected{% endif %}>Low Stock</option>
            </select>

            <!-- Bulk Actions -->
            <div class="flex items-center space-x-2">
                <select id="bulk-action" class="form-select">
                    <option value="">Bulk Actions</option>
                    <option value="activate">Activate Selected</option>
                    <option value="deactivate">Deactivate Selected</option>
                    <option value="delete">Delete Selected</option>
                </select>
                <button onclick="executeBulkAction()" class="btn btn-secondary btn-sm">Apply</button>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    {% if products.items %}
    <div class="glass-card overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" id="select-all" class="rounded">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Product
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Category
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Price
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Stock
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Created
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for product in products.items %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <input type="checkbox" class="product-checkbox rounded" value="{{ product.id }}">
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <img src="{{ product.image_url or '/static/images/placeholder.jpg' }}" 
                                     alt="{{ product.name }}" 
                                     class="w-12 h-12 object-cover rounded-lg mr-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                                    <div class="text-sm text-gray-500">{{ product.description[:50] }}{% if product.description and product.description|length > 50 %}...{% endif %}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if product.category == 'giftcard' %}bg-green-100 text-green-800
                                {% elif product.category == 'followers' %}bg-pink-100 text-pink-800
                                {% elif product.category == 'gaming_tokens' %}bg-purple-100 text-purple-800
                                {% endif %}">
                                {{ product.category.replace('_', ' ').title() }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            ${{ "%.2f"|format(product.price) }}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <span class="{% if product.stock < 10 %}text-red-600 font-semibold{% endif %}">
                                {{ product.stock }}
                            </span>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if product.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if product.is_active %}Active{% else %}Inactive{% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500">
                            {{ product.created_at.strftime('%b %d, %Y') }}
                        </td>
                        <td class="px-6 py-4 text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <a href="{{ url_for('main.product_detail', product_id=product.id) }}" 
                                   class="text-blue-600 hover:text-blue-900" 
                                   title="View Product">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </a>
                                
                                <a href="{{ url_for('admin.edit_product', product_id=product.id) }}" 
                                   class="text-indigo-600 hover:text-indigo-900"
                                   title="Edit Product">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </a>
                                
                                <form method="POST" action="{{ url_for('admin.delete_product', product_id=product.id) }}" 
                                      class="inline" 
                                      onsubmit="return confirm('Are you sure you want to delete this product?')">
                                    {{ csrf_token() }}
                                    <button type="submit" 
                                            class="text-red-600 hover:text-red-900"
                                            title="Delete Product">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if products.pages > 1 %}
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if products.has_prev %}
                        <a href="{{ url_for('admin.products', page=products.prev_num, category=current_category, search=current_search, status=current_status) }}" 
                           class="btn btn-secondary btn-sm">Previous</a>
                    {% endif %}
                    {% if products.has_next %}
                        <a href="{{ url_for('admin.products', page=products.next_num, category=current_category, search=current_search, status=current_status) }}" 
                           class="btn btn-secondary btn-sm">Next</a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing {{ ((products.page - 1) * products.per_page) + 1 }} to 
                            {{ products.page * products.per_page if products.page * products.per_page < products.total else products.total }} 
                            of {{ products.total }} results
                        </p>
                    </div>
                    <div>
                        <nav class="flex items-center space-x-2">
                            {% if products.has_prev %}
                                <a href="{{ url_for('admin.products', page=products.prev_num, category=current_category, search=current_search, status=current_status) }}" 
                                   class="btn btn-secondary btn-sm">Previous</a>
                            {% endif %}
                            
                            {% for page_num in products.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != products.page %}
                                        <a href="{{ url_for('admin.products', page=page_num, category=current_category, search=current_search, status=current_status) }}" 
                                           class="btn btn-secondary btn-sm">{{ page_num }}</a>
                                    {% else %}
                                        <span class="btn btn-primary btn-sm">{{ page_num }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="px-3 py-1">…</span>
                                {% endif %}
                            {% endfor %}
                            
                            {% if products.has_next %}
                                <a href="{{ url_for('admin.products', page=products.next_num, category=current_category, search=current_search, status=current_status) }}" 
                                   class="btn btn-secondary btn-sm">Next</a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% else %}
    <!-- No Products -->
    <div class="glass-card p-12 text-center">
        <svg class="w-24 h-24 mx-auto text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
        </svg>
        
        <h2 class="text-2xl font-bold text-gray-900 mb-4">No products found</h2>
        <p class="text-gray-600 mb-8 max-w-md mx-auto">
            {% if current_search or current_category or current_status %}
                No products match your current filters. Try adjusting your search criteria.
            {% else %}
                You haven't added any products yet. Start by creating your first product.
            {% endif %}
        </p>
        
        <div class="space-y-4">
            <a href="{{ url_for('admin.add_product') }}" class="btn btn-primary btn-lg">
                Add Your First Product
            </a>
            
            {% if current_search or current_category or current_status %}
            <div>
                <a href="{{ url_for('admin.products') }}" class="text-blue-600 hover:text-blue-800">
                    Clear All Filters
                </a>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Handle filters
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const categoryFilter = document.getElementById('category-filter');
    const statusFilter = document.getElementById('status-filter');
    const selectAllCheckbox = document.getElementById('select-all');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');
    
    // Search functionality
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            updateFilters();
        }, 500);
    });
    
    // Filter changes
    categoryFilter.addEventListener('change', updateFilters);
    statusFilter.addEventListener('change', updateFilters);
    
    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        productCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Individual checkbox changes
    productCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(productCheckboxes).every(cb => cb.checked);
            const noneChecked = Array.from(productCheckboxes).every(cb => !cb.checked);
            
            selectAllCheckbox.checked = allChecked;
            selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
        });
    });
    
    function updateFilters() {
        const url = new URL(window.location);
        
        // Update search parameter
        if (searchInput.value) {
            url.searchParams.set('search', searchInput.value);
        } else {
            url.searchParams.delete('search');
        }
        
        // Update category parameter
        if (categoryFilter.value) {
            url.searchParams.set('category', categoryFilter.value);
        } else {
            url.searchParams.delete('category');
        }
        
        // Update status parameter
        if (statusFilter.value) {
            url.searchParams.set('status', statusFilter.value);
        } else {
            url.searchParams.delete('status');
        }
        
        // Reset to first page
        url.searchParams.delete('page');
        
        window.location.href = url.toString();
    }
});

function executeBulkAction() {
    const action = document.getElementById('bulk-action').value;
    const selectedProducts = Array.from(document.querySelectorAll('.product-checkbox:checked')).map(cb => cb.value);
    
    if (!action) {
        toast.show('Please select an action', 'warning');
        return;
    }
    
    if (selectedProducts.length === 0) {
        toast.show('Please select at least one product', 'warning');
        return;
    }
    
    const actionText = action === 'delete' ? 'delete' : action;
    if (confirm(`Are you sure you want to ${actionText} ${selectedProducts.length} product(s)?`)) {
        // In a real application, you would send this to the server
        toast.show(`Bulk ${actionText} operation would be performed on ${selectedProducts.length} products`, 'info');
        
        // Reset selections
        document.getElementById('select-all').checked = false;
        document.querySelectorAll('.product-checkbox').forEach(cb => cb.checked = false);
        document.getElementById('bulk-action').value = '';
    }
}
</script>
{% endblock %}
