{% extends "base.html" %}

{% block title %}User Management - Digital Nashop Admin{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">User Management</h1>
            <p class="text-gray-600">Manage customer accounts and permissions</p>
        </div>
    </div>

    <!-- Filters -->
    <div class="glass-card p-4 mb-6">
        <div class="flex flex-wrap items-center gap-4">
            <!-- Search -->
            <div class="flex-1 min-w-64">
                <input type="text" 
                       id="user-search"
                       class="form-input" 
                       placeholder="Search by username or email..."
                       value="{{ search }}">
            </div>

            <!-- Role Filter -->
            <select id="role-filter" class="form-select">
                <option value="">All Roles</option>
                <option value="user" {% if role_filter == 'user' %}selected{% endif %}>Customers</option>
                <option value="admin" {% if role_filter == 'admin' %}selected{% endif %}>Administrators</option>
            </select>
        </div>
    </div>

    <!-- Users Table -->
    {% if users.items %}
    <div class="glass-card overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for user in users.items %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div>
                                <div class="text-sm font-medium text-gray-900">{{ user.username }}</div>
                                <div class="text-sm text-gray-500">{{ user.email }}</div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if user.role == 'admin' %}bg-purple-100 text-purple-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                                {{ user.role.title() }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">${{ "%.2f"|format(user.balance) }}</td>
                        <td class="px-6 py-4 text-sm text-gray-500">{{ user.created_at.strftime('%b %d, %Y') }}</td>
                        <td class="px-6 py-4 text-right">
                            {% if user.id != current_user.id %}
                            <form method="POST" action="{{ url_for('admin.toggle_user_role', user_id=user.id) }}" class="inline">
                                {{ csrf_token() }}
                                <button type="submit" 
                                        class="text-indigo-600 hover:text-indigo-900 text-sm"
                                        onclick="return confirm('Change user role?')">
                                    {% if user.role == 'user' %}Make Admin{% else %}Make User{% endif %}
                                </button>
                            </form>
                            {% else %}
                            <span class="text-gray-400 text-sm">Current User</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if users.pages > 1 %}
        <div class="bg-white px-4 py-3 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing {{ ((users.page - 1) * users.per_page) + 1 }} to 
                        {{ users.page * users.per_page if users.page * users.per_page < users.total else users.total }} 
                        of {{ users.total }} results
                    </p>
                </div>
                <div>
                    <nav class="flex items-center space-x-2">
                        {% if users.has_prev %}
                            <a href="{{ url_for('admin.users', page=users.prev_num, search=search, role=role_filter) }}" 
                               class="btn btn-secondary btn-sm">Previous</a>
                        {% endif %}
                        
                        {% for page_num in users.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != users.page %}
                                    <a href="{{ url_for('admin.users', page=page_num, search=search, role=role_filter) }}" 
                                       class="btn btn-secondary btn-sm">{{ page_num }}</a>
                                {% else %}
                                    <span class="btn btn-primary btn-sm">{{ page_num }}</span>
                                {% endif %}
                            {% else %}
                                <span class="px-3 py-1">…</span>
                            {% endif %}
                        {% endfor %}
                        
                        {% if users.has_next %}
                            <a href="{{ url_for('admin.users', page=users.next_num, search=search, role=role_filter) }}" 
                               class="btn btn-secondary btn-sm">Next</a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% else %}
    <!-- No Users -->
    <div class="glass-card p-12 text-center">
        <svg class="w-24 h-24 mx-auto text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
        </svg>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">No users found</h2>
        <p class="text-gray-600">No users match your current filters.</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const userSearch = document.getElementById('user-search');
    const roleFilter = document.getElementById('role-filter');
    
    let searchTimeout;
    userSearch.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            updateFilters();
        }, 500);
    });
    
    roleFilter.addEventListener('change', updateFilters);
    
    function updateFilters() {
        const url = new URL(window.location);
        
        if (userSearch.value) {
            url.searchParams.set('search', userSearch.value);
        } else {
            url.searchParams.delete('search');
        }
        
        if (roleFilter.value) {
            url.searchParams.set('role', roleFilter.value);
        } else {
            url.searchParams.delete('role');
        }
        
        url.searchParams.delete('page');
        window.location.href = url.toString();
    }
});
</script>
{% endblock %}
