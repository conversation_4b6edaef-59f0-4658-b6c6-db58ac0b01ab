from flask import Blueprint, render_template, request, jsonify
from models import Product, db
from sqlalchemy import or_

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Homepage with featured products"""
    # Get featured products (latest 6 active products)
    featured_products = Product.query.filter_by(is_active=True).order_by(Product.created_at.desc()).limit(6).all()
    
    # Get product counts by category
    categories = {
        'giftcard': Product.query.filter_by(category='giftcard', is_active=True).count(),
        'followers': Product.query.filter_by(category='followers', is_active=True).count(),
        'gaming_tokens': Product.query.filter_by(category='gaming_tokens', is_active=True).count()
    }
    
    return render_template('main/index.html', featured_products=featured_products, categories=categories)

@main_bp.route('/products')
def products():
    """Product listing page with filtering and search"""
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '')
    search = request.args.get('search', '')
    sort_by = request.args.get('sort', 'newest')
    
    # Base query for active products
    query = Product.query.filter_by(is_active=True)
    
    # Apply category filter
    if category and category in ['giftcard', 'followers', 'gaming_tokens']:
        query = query.filter_by(category=category)
    
    # Apply search filter
    if search:
        search_term = f"%{search}%"
        query = query.filter(or_(
            Product.name.ilike(search_term),
            Product.description.ilike(search_term)
        ))
    
    # Apply sorting
    if sort_by == 'price_low':
        query = query.order_by(Product.price.asc())
    elif sort_by == 'price_high':
        query = query.order_by(Product.price.desc())
    elif sort_by == 'name':
        query = query.order_by(Product.name.asc())
    else:  # newest
        query = query.order_by(Product.created_at.desc())
    
    # Paginate results
    per_page = 12
    products = query.paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    # Get all categories for filter sidebar
    categories = {
        'giftcard': Product.query.filter_by(category='giftcard', is_active=True).count(),
        'followers': Product.query.filter_by(category='followers', is_active=True).count(),
        'gaming_tokens': Product.query.filter_by(category='gaming_tokens', is_active=True).count()
    }
    
    return render_template('main/products.html', 
                         products=products, 
                         categories=categories,
                         current_category=category,
                         current_search=search,
                         current_sort=sort_by)

@main_bp.route('/product/<int:product_id>')
def product_detail(product_id):
    """Product detail page"""
    product = Product.query.get_or_404(product_id)
    
    if not product.is_active:
        return render_template('errors/404.html'), 404
    
    # Get related products (same category, excluding current product)
    related_products = Product.query.filter(
        Product.category == product.category,
        Product.id != product.id,
        Product.is_active == True
    ).limit(4).all()
    
    return render_template('main/product_detail.html', 
                         product=product, 
                         related_products=related_products)

@main_bp.route('/search')
def search():
    """AJAX search endpoint"""
    query = request.args.get('q', '').strip()
    
    if len(query) < 2:
        return jsonify({'products': []})
    
    search_term = f"%{query}%"
    products = Product.query.filter(
        Product.is_active == True,
        or_(
            Product.name.ilike(search_term),
            Product.description.ilike(search_term)
        )
    ).limit(10).all()
    
    results = []
    for product in products:
        results.append({
            'id': product.id,
            'name': product.name,
            'price': float(product.price),
            'image_url': product.image_url,
            'category': product.category
        })
    
    return jsonify({'products': results})

@main_bp.route('/about')
def about():
    """About page"""
    return render_template('main/about.html')

@main_bp.route('/contact')
def contact():
    """Contact page"""
    return render_template('main/contact.html')

@main_bp.route('/terms')
def terms():
    """Terms of service page"""
    return render_template('main/terms.html')

@main_bp.route('/privacy')
def privacy():
    """Privacy policy page"""
    return render_template('main/privacy.html')
