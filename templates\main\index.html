{% extends "base.html" %}

{% block title %}Digital Nashop - Premium Digital Products{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative overflow-hidden">
    <div class="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 opacity-90"></div>
    <div class="relative container mx-auto px-4 py-20 text-center text-white">
        <h1 class="text-5xl md:text-7xl font-bold mb-6">
            Digital <span class="text-yellow-300">Nashop</span>
        </h1>
        <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Your premium marketplace for digital products. Get instant access to gift cards, social media followers, and gaming tokens.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ url_for('main.products') }}" class="btn btn-primary btn-lg">
                Shop Now
            </a>
            {% if not current_user.is_authenticated %}
                <a href="{{ url_for('auth.register') }}" class="btn btn-secondary btn-lg">
                    Join Today
                </a>
            {% endif %}
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-12">Shop by Category</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Gift Cards -->
            <div class="glass-card p-8 text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold mb-4">Gift Cards</h3>
                <p class="text-gray-600 mb-6">Premium gift cards for popular platforms and services. Instant delivery guaranteed.</p>
                <p class="text-sm text-gray-500 mb-4">{{ categories.giftcard }} products available</p>
                <a href="{{ url_for('main.products', category='giftcard') }}" class="btn btn-primary">
                    Browse Gift Cards
                </a>
            </div>

            <!-- Social Media Followers -->
            <div class="glass-card p-8 text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-pink-400 to-pink-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold mb-4">Social Media</h3>
                <p class="text-gray-600 mb-6">Boost your social presence with real followers and engagement across platforms.</p>
                <p class="text-sm text-gray-500 mb-4">{{ categories.followers }} products available</p>
                <a href="{{ url_for('main.products', category='followers') }}" class="btn btn-primary">
                    Browse Social Media
                </a>
            </div>

            <!-- Gaming Tokens -->
            <div class="glass-card p-8 text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold mb-4">Gaming Tokens</h3>
                <p class="text-gray-600 mb-6">In-game currencies and tokens for popular games. Level up your gaming experience.</p>
                <p class="text-sm text-gray-500 mb-4">{{ categories.gaming_tokens }} products available</p>
                <a href="{{ url_for('main.products', category='gaming_tokens') }}" class="btn btn-primary">
                    Browse Gaming
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Featured Products -->
{% if featured_products %}
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-12">Featured Products</h2>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for product in featured_products %}
            <div class="product-card">
                <div class="relative">
                    <img src="{{ product.image_url or '/static/images/placeholder.jpg' }}" 
                         alt="{{ product.name }}" 
                         class="product-image">
                    <div class="absolute top-4 right-4">
                        <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            {{ product.category.replace('_', ' ').title() }}
                        </span>
                    </div>
                </div>
                
                <div class="p-6">
                    <h3 class="text-xl font-bold mb-2">{{ product.name }}</h3>
                    <p class="text-gray-600 mb-4 line-clamp-2">{{ product.description or 'Premium digital product with instant delivery.' }}</p>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-2xl font-bold text-blue-600">${{ "%.2f"|format(product.price) }}</span>
                        <div class="flex items-center space-x-2">
                            {% if product.stock > 0 %}
                                <span class="text-green-600 text-sm">In Stock</span>
                            {% else %}
                                <span class="text-red-600 text-sm">Out of Stock</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mt-4 flex space-x-2">
                        <a href="{{ url_for('main.product_detail', product_id=product.id) }}" 
                           class="btn btn-secondary flex-1 text-center">
                            View Details
                        </a>
                        {% if current_user.is_authenticated and product.stock > 0 %}
                            <button onclick="cart.addToCart({{ product.id }})" 
                                    class="btn btn-primary px-4">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
                                </svg>
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ url_for('main.products') }}" class="btn btn-primary btn-lg">
                View All Products
            </a>
        </div>
    </div>
</section>
{% endif %}

<!-- Features Section -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <h2 class="text-4xl font-bold text-center mb-12">Why Choose Digital Nashop?</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4">Instant Delivery</h3>
                <p class="text-gray-600">Get your digital products delivered instantly to your account. No waiting, no delays.</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4">Secure & Trusted</h3>
                <p class="text-gray-600">Your transactions are protected with industry-standard security measures and encryption.</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold mb-4">24/7 Support</h3>
                <p class="text-gray-600">Our dedicated support team is available around the clock to help with any questions.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
{% if not current_user.is_authenticated %}
<section class="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
    <div class="container mx-auto px-4 text-center text-white">
        <h2 class="text-4xl font-bold mb-6">Ready to Get Started?</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied customers and start shopping for premium digital products today.
        </p>
        <a href="{{ url_for('auth.register') }}" class="btn btn-secondary btn-lg">
            Create Your Account
        </a>
    </div>
</section>
{% endif %}
{% endblock %}
