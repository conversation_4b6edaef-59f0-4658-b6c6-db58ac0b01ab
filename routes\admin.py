from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import Product, Order, Transaction, User, db
from datetime import datetime, timedelta
from functools import wraps

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    """Decorator to require admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('Access denied. Admin privileges required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/')
@login_required
@admin_required
def dashboard():
    """Admin dashboard with analytics"""
    # Calculate date ranges
    today = datetime.utcnow().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)
    
    # Sales analytics
    total_revenue = db.session.query(db.func.sum(Order.total_price)).filter(
        Order.status.in_(['processing', 'delivered'])
    ).scalar() or 0
    
    weekly_revenue = db.session.query(db.func.sum(Order.total_price)).filter(
        Order.status.in_(['processing', 'delivered']),
        Order.created_at >= week_ago
    ).scalar() or 0
    
    monthly_revenue = db.session.query(db.func.sum(Order.total_price)).filter(
        Order.status.in_(['processing', 'delivered']),
        Order.created_at >= month_ago
    ).scalar() or 0
    
    # Order statistics
    total_orders = Order.query.count()
    pending_orders = Order.query.filter_by(status='pending').count()
    processing_orders = Order.query.filter_by(status='processing').count()
    
    # User statistics
    total_users = User.query.filter_by(role='user').count()
    new_users_week = User.query.filter(
        User.role == 'user',
        User.created_at >= week_ago
    ).count()
    
    # Product statistics
    total_products = Product.query.count()
    active_products = Product.query.filter_by(is_active=True).count()
    low_stock_products = Product.query.filter(Product.stock < 10, Product.is_active == True).count()
    
    # Recent orders
    recent_orders = Order.query.order_by(Order.created_at.desc()).limit(10).all()
    
    # Popular products (by order count)
    popular_products = db.session.query(
        Product.name,
        db.func.count(Order.id).label('order_count'),
        db.func.sum(Order.total_price).label('total_revenue')
    ).join(Order).filter(
        Order.created_at >= month_ago
    ).group_by(Product.id).order_by(db.desc('order_count')).limit(5).all()
    
    return render_template('admin/dashboard.html',
                         total_revenue=total_revenue,
                         weekly_revenue=weekly_revenue,
                         monthly_revenue=monthly_revenue,
                         total_orders=total_orders,
                         pending_orders=pending_orders,
                         processing_orders=processing_orders,
                         total_users=total_users,
                         new_users_week=new_users_week,
                         total_products=total_products,
                         active_products=active_products,
                         low_stock_products=low_stock_products,
                         recent_orders=recent_orders,
                         popular_products=popular_products)

@admin_bp.route('/products')
@login_required
@admin_required
def products():
    """Product management page"""
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '')
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    
    query = Product.query
    
    # Apply filters
    if category and category in ['giftcard', 'followers', 'gaming_tokens']:
        query = query.filter_by(category=category)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(Product.name.ilike(search_term))
    
    if status == 'active':
        query = query.filter_by(is_active=True)
    elif status == 'inactive':
        query = query.filter_by(is_active=False)
    elif status == 'low_stock':
        query = query.filter(Product.stock < 10)
    
    products = query.order_by(Product.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/products.html', 
                         products=products,
                         current_category=category,
                         current_search=search,
                         current_status=status)

@admin_bp.route('/products/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_product():
    """Add new product"""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        category = request.form.get('category', '')
        description = request.form.get('description', '').strip()
        price = request.form.get('price', type=float)
        image_url = request.form.get('image_url', '').strip()
        stock = request.form.get('stock', type=int)
        
        errors = []
        
        if not name or len(name) < 3:
            errors.append('Product name must be at least 3 characters long')
        
        if category not in ['giftcard', 'followers', 'gaming_tokens']:
            errors.append('Please select a valid category')
        
        if not price or price <= 0:
            errors.append('Price must be greater than 0')
        
        if stock is None or stock < 0:
            errors.append('Stock must be 0 or greater')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('admin/add_product.html')
        
        try:
            product = Product(
                name=name,
                category=category,
                description=description,
                price=price,
                image_url=image_url if image_url else None,
                stock=stock,
                is_active=True
            )
            
            db.session.add(product)
            db.session.commit()
            
            flash('Product added successfully!', 'success')
            return redirect(url_for('admin.products'))
            
        except Exception as e:
            db.session.rollback()
            flash('Failed to add product. Please try again.', 'error')
    
    return render_template('admin/add_product.html')

@admin_bp.route('/products/<int:product_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_product(product_id):
    """Edit existing product"""
    product = Product.query.get_or_404(product_id)
    
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        category = request.form.get('category', '')
        description = request.form.get('description', '').strip()
        price = request.form.get('price', type=float)
        image_url = request.form.get('image_url', '').strip()
        stock = request.form.get('stock', type=int)
        is_active = bool(request.form.get('is_active'))
        
        errors = []
        
        if not name or len(name) < 3:
            errors.append('Product name must be at least 3 characters long')
        
        if category not in ['giftcard', 'followers', 'gaming_tokens']:
            errors.append('Please select a valid category')
        
        if not price or price <= 0:
            errors.append('Price must be greater than 0')
        
        if stock is None or stock < 0:
            errors.append('Stock must be 0 or greater')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('admin/edit_product.html', product=product)
        
        try:
            product.name = name
            product.category = category
            product.description = description
            product.price = price
            product.image_url = image_url if image_url else None
            product.stock = stock
            product.is_active = is_active
            
            db.session.commit()
            
            flash('Product updated successfully!', 'success')
            return redirect(url_for('admin.products'))
            
        except Exception as e:
            db.session.rollback()
            flash('Failed to update product. Please try again.', 'error')
    
    return render_template('admin/edit_product.html', product=product)

@admin_bp.route('/products/<int:product_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_product(product_id):
    """Delete product"""
    product = Product.query.get_or_404(product_id)

    try:
        # Check if product has orders
        order_count = Order.query.filter_by(product_id=product_id).count()

        if order_count > 0:
            # Don't delete, just deactivate
            product.is_active = False
            db.session.commit()
            flash('Product deactivated (has existing orders)', 'info')
        else:
            # Safe to delete
            db.session.delete(product)
            db.session.commit()
            flash('Product deleted successfully!', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Failed to delete product. Please try again.', 'error')

    return redirect(url_for('admin.products'))

@admin_bp.route('/orders')
@login_required
@admin_required
def orders():
    """Order management page"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')
    user_search = request.args.get('user', '')

    query = Order.query

    if status_filter and status_filter in ['pending', 'processing', 'delivered', 'cancelled']:
        query = query.filter_by(status=status_filter)

    if user_search:
        query = query.join(User).filter(User.username.ilike(f"%{user_search}%"))

    orders = query.order_by(Order.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/orders.html',
                         orders=orders,
                         status_filter=status_filter,
                         user_search=user_search)

@admin_bp.route('/orders/<int:order_id>/update-status', methods=['POST'])
@login_required
@admin_required
def update_order_status(order_id):
    """Update order status"""
    order = Order.query.get_or_404(order_id)
    new_status = request.form.get('status')

    if new_status not in ['pending', 'processing', 'delivered', 'cancelled']:
        flash('Invalid status', 'error')
        return redirect(url_for('admin.orders'))

    try:
        old_status = order.status
        order.update_status(new_status)

        # Handle refunds for cancelled orders
        if new_status == 'cancelled' and old_status in ['pending', 'processing']:
            # Refund to user wallet
            order.user.add_to_balance(float(order.total_price))

            # Restore product stock
            order.product.stock += order.quantity

            # Create refund transaction
            transaction = Transaction(
                user_id=order.user_id,
                order_id=order.id,
                type='refund',
                amount=order.total_price,
                description=f"Refund for cancelled order #{order.id}"
            )
            db.session.add(transaction)

        db.session.commit()
        flash(f'Order status updated to {new_status}', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Failed to update order status. Please try again.', 'error')

    return redirect(url_for('admin.orders'))

@admin_bp.route('/users')
@login_required
@admin_required
def users():
    """User management page"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    role_filter = request.args.get('role', '')

    query = User.query

    if search:
        search_term = f"%{search}%"
        query = query.filter(
            db.or_(
                User.username.ilike(search_term),
                User.email.ilike(search_term)
            )
        )

    if role_filter and role_filter in ['user', 'admin']:
        query = query.filter_by(role=role_filter)

    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/users.html',
                         users=users,
                         search=search,
                         role_filter=role_filter)

@admin_bp.route('/users/<int:user_id>/toggle-role', methods=['POST'])
@login_required
@admin_required
def toggle_user_role(user_id):
    """Toggle user role between user and admin"""
    user = User.query.get_or_404(user_id)

    if user.id == current_user.id:
        flash('Cannot modify your own role', 'error')
        return redirect(url_for('admin.users'))

    try:
        user.role = 'admin' if user.role == 'user' else 'user'
        db.session.commit()

        flash(f'User role updated to {user.role}', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Failed to update user role. Please try again.', 'error')

    return redirect(url_for('admin.users'))

@admin_bp.route('/transactions')
@login_required
@admin_required
def transactions():
    """Transaction logs page"""
    page = request.args.get('page', 1, type=int)
    type_filter = request.args.get('type', '')
    user_search = request.args.get('user', '')

    query = Transaction.query

    if type_filter and type_filter in ['purchase', 'refund', 'wallet_credit']:
        query = query.filter_by(type=type_filter)

    if user_search:
        query = query.join(User).filter(User.username.ilike(f"%{user_search}%"))

    transactions = query.order_by(Transaction.created_at.desc()).paginate(
        page=page, per_page=25, error_out=False
    )

    return render_template('admin/transactions.html',
                         transactions=transactions,
                         type_filter=type_filter,
                         user_search=user_search)
