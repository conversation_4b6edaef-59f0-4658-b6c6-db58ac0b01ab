{% extends "base.html" %}

{% block title %}Products - Digital Nashop{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">
            {% if current_category %}
                {{ current_category.replace('_', ' ').title() }} Products
            {% else %}
                All Products
            {% endif %}
        </h1>
        <p class="text-gray-600 max-w-2xl mx-auto">
            Discover our premium collection of digital products with instant delivery and guaranteed satisfaction.
        </p>
    </div>

    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:w-1/4">
            <div class="glass-card p-6 sticky top-24">
                <h3 class="text-lg font-semibold mb-4">Filters</h3>
                
                <!-- Search -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Search Products</label>
                    <div class="relative search-container">
                        <input type="text" 
                               id="search-input"
                               class="form-input pr-10" 
                               placeholder="Search products..."
                               value="{{ current_search }}">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <!-- Search Results Dropdown -->
                        <div id="search-results" class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-64 overflow-y-auto" style="display: none;"></div>
                    </div>
                </div>

                <!-- Categories -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Categories</label>
                    <div class="space-y-2">
                        <a href="{{ url_for('main.products') }}" 
                           class="block p-2 rounded-lg transition-colors {% if not current_category %}bg-blue-100 text-blue-800{% else %}hover:bg-gray-100{% endif %}">
                            <div class="flex justify-between items-center">
                                <span>All Products</span>
                                <span class="text-sm text-gray-500">{{ categories.giftcard + categories.followers + categories.gaming_tokens }}</span>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('main.products', category='giftcard') }}" 
                           class="block p-2 rounded-lg transition-colors {% if current_category == 'giftcard' %}bg-blue-100 text-blue-800{% else %}hover:bg-gray-100{% endif %}">
                            <div class="flex justify-between items-center">
                                <span>Gift Cards</span>
                                <span class="text-sm text-gray-500">{{ categories.giftcard }}</span>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('main.products', category='followers') }}" 
                           class="block p-2 rounded-lg transition-colors {% if current_category == 'followers' %}bg-blue-100 text-blue-800{% else %}hover:bg-gray-100{% endif %}">
                            <div class="flex justify-between items-center">
                                <span>Social Media</span>
                                <span class="text-sm text-gray-500">{{ categories.followers }}</span>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('main.products', category='gaming_tokens') }}" 
                           class="block p-2 rounded-lg transition-colors {% if current_category == 'gaming_tokens' %}bg-blue-100 text-blue-800{% else %}hover:bg-gray-100{% endif %}">
                            <div class="flex justify-between items-center">
                                <span>Gaming Tokens</span>
                                <span class="text-sm text-gray-500">{{ categories.gaming_tokens }}</span>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Sort Options -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                    <select id="sort-select" class="form-select">
                        <option value="newest" {% if current_sort == 'newest' %}selected{% endif %}>Newest First</option>
                        <option value="price_low" {% if current_sort == 'price_low' %}selected{% endif %}>Price: Low to High</option>
                        <option value="price_high" {% if current_sort == 'price_high' %}selected{% endif %}>Price: High to Low</option>
                        <option value="name" {% if current_sort == 'name' %}selected{% endif %}>Name A-Z</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="lg:w-3/4">
            <!-- Results Info -->
            <div class="flex justify-between items-center mb-6">
                <p class="text-gray-600">
                    Showing {{ products.items|length }} of {{ products.total }} products
                    {% if current_search %}for "{{ current_search }}"{% endif %}
                </p>
                
                <!-- Mobile Filter Toggle -->
                <button class="lg:hidden btn btn-secondary btn-sm" onclick="toggleMobileFilters()">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    Filters
                </button>
            </div>

            {% if products.items %}
                <!-- Products Grid -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {% for product in products.items %}
                    <div class="product-card">
                        <div class="relative">
                            <img src="{{ product.image_url or '/static/images/placeholder.jpg' }}" 
                                 alt="{{ product.name }}" 
                                 class="product-image">
                            
                            <!-- Category Badge -->
                            <div class="absolute top-4 left-4">
                                <span class="bg-white bg-opacity-90 text-gray-800 px-2 py-1 rounded-full text-xs font-semibold">
                                    {{ product.category.replace('_', ' ').title() }}
                                </span>
                            </div>
                            
                            <!-- Stock Status -->
                            <div class="absolute top-4 right-4">
                                {% if product.stock > 0 %}
                                    <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                        In Stock
                                    </span>
                                {% else %}
                                    <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                        Out of Stock
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">{{ product.name }}</h3>
                            <p class="text-gray-600 mb-4 line-clamp-2">
                                {{ product.description or 'Premium digital product with instant delivery.' }}
                            </p>
                            
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-2xl font-bold text-blue-600">
                                    ${{ "%.2f"|format(product.price) }}
                                </span>
                                {% if product.stock > 0 %}
                                    <span class="text-sm text-gray-500">{{ product.stock }} available</span>
                                {% endif %}
                            </div>
                            
                            <div class="flex space-x-2">
                                <a href="{{ url_for('main.product_detail', product_id=product.id) }}" 
                                   class="btn btn-secondary flex-1 text-center">
                                    View Details
                                </a>
                                
                                {% if current_user.is_authenticated and product.stock > 0 %}
                                    <button onclick="cart.addToCart({{ product.id }})" 
                                            class="btn btn-primary px-4">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
                                        </svg>
                                    </button>
                                {% elif not current_user.is_authenticated %}
                                    <a href="{{ url_for('auth.login') }}" 
                                       class="btn btn-primary px-4">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if products.pages > 1 %}
                <div class="flex justify-center">
                    <nav class="flex items-center space-x-2">
                        {% if products.has_prev %}
                            <a href="{{ url_for('main.products', page=products.prev_num, category=current_category, search=current_search, sort=current_sort) }}" 
                               class="btn btn-secondary btn-sm">
                                Previous
                            </a>
                        {% endif %}
                        
                        {% for page_num in products.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != products.page %}
                                    <a href="{{ url_for('main.products', page=page_num, category=current_category, search=current_search, sort=current_sort) }}" 
                                       class="btn btn-secondary btn-sm">
                                        {{ page_num }}
                                    </a>
                                {% else %}
                                    <span class="btn btn-primary btn-sm">{{ page_num }}</span>
                                {% endif %}
                            {% else %}
                                <span class="px-3 py-1">…</span>
                            {% endif %}
                        {% endfor %}
                        
                        {% if products.has_next %}
                            <a href="{{ url_for('main.products', page=products.next_num, category=current_category, search=current_search, sort=current_sort) }}" 
                               class="btn btn-secondary btn-sm">
                                Next
                            </a>
                        {% endif %}
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <!-- No Products Found -->
                <div class="text-center py-12">
                    <svg class="w-24 h-24 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                    </svg>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
                    <p class="text-gray-600 mb-4">
                        {% if current_search %}
                            No products match your search "{{ current_search }}". Try different keywords.
                        {% else %}
                            No products available in this category at the moment.
                        {% endif %}
                    </p>
                    <a href="{{ url_for('main.products') }}" class="btn btn-primary">
                        View All Products
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Handle search and sort changes
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-input');
    const sortSelect = document.getElementById('sort-select');
    
    // Handle sort change
    sortSelect.addEventListener('change', function() {
        updateURL({ sort: this.value, page: 1 });
    });
    
    // Handle search
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            updateURL({ search: this.value, page: 1 });
        }, 500);
    });
    
    function updateURL(params) {
        const url = new URL(window.location);
        Object.keys(params).forEach(key => {
            if (params[key]) {
                url.searchParams.set(key, params[key]);
            } else {
                url.searchParams.delete(key);
            }
        });
        window.location.href = url.toString();
    }
});

// Mobile filters toggle
function toggleMobileFilters() {
    // This would show/hide the sidebar on mobile
    // Implementation depends on your mobile layout preferences
    alert('Mobile filters toggle - implement based on your design preferences');
}
</script>
{% endblock %}
