<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}Digital Nashop - Premium Digital Products{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    <!-- Additional CSS -->
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="{{ url_for('main.index') }}" class="text-2xl font-bold text-gradient">
                        Digital Nashop
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-6">
                    <a href="{{ url_for('main.index') }}" class="nav-link {% if request.endpoint == 'main.index' %}active{% endif %}">
                        Home
                    </a>
                    <a href="{{ url_for('main.products') }}" class="nav-link {% if request.endpoint == 'main.products' %}active{% endif %}">
                        Products
                    </a>
                    
                    {% if current_user.is_authenticated %}
                        {% if current_user.is_admin() %}
                            <a href="{{ url_for('admin.dashboard') }}" class="nav-link {% if request.blueprint == 'admin' %}active{% endif %}">
                                Admin
                            </a>
                        {% endif %}
                        
                        <a href="{{ url_for('customer.cart') }}" class="nav-link relative {% if request.endpoint == 'customer.cart' %}active{% endif %}">
                            Cart
                            <span class="cart-count absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" style="display: none;">0</span>
                        </a>
                        
                        <div class="relative group">
                            <button class="nav-link flex items-center space-x-1">
                                <span>{{ current_user.username }}</span>
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            
                            <!-- Dropdown Menu -->
                            <div class="absolute right-0 mt-2 w-48 glass-card opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                <div class="py-2">
                                    <a href="{{ url_for('customer.dashboard') }}" class="block px-4 py-2 text-sm hover:bg-gray-100">Dashboard</a>
                                    <a href="{{ url_for('customer.orders') }}" class="block px-4 py-2 text-sm hover:bg-gray-100">Orders</a>
                                    <a href="{{ url_for('customer.wallet') }}" class="block px-4 py-2 text-sm hover:bg-gray-100">
                                        Wallet (${{ "%.2f"|format(current_user.balance) }})
                                    </a>
                                    <a href="{{ url_for('customer.profile') }}" class="block px-4 py-2 text-sm hover:bg-gray-100">Profile</a>
                                    <hr class="my-2">
                                    <a href="{{ url_for('auth.logout') }}" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Logout</a>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <a href="{{ url_for('auth.login') }}" class="nav-link">Login</a>
                        <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-sm">Sign Up</a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="nav-link">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="md:hidden hidden">
                <div class="px-2 pt-2 pb-3 space-y-1">
                    <a href="{{ url_for('main.index') }}" class="block nav-link">Home</a>
                    <a href="{{ url_for('main.products') }}" class="block nav-link">Products</a>
                    
                    {% if current_user.is_authenticated %}
                        {% if current_user.is_admin() %}
                            <a href="{{ url_for('admin.dashboard') }}" class="block nav-link">Admin</a>
                        {% endif %}
                        <a href="{{ url_for('customer.cart') }}" class="block nav-link">Cart</a>
                        <a href="{{ url_for('customer.dashboard') }}" class="block nav-link">Dashboard</a>
                        <a href="{{ url_for('customer.orders') }}" class="block nav-link">Orders</a>
                        <a href="{{ url_for('customer.wallet') }}" class="block nav-link">Wallet</a>
                        <a href="{{ url_for('customer.profile') }}" class="block nav-link">Profile</a>
                        <a href="{{ url_for('auth.logout') }}" class="block nav-link text-red-600">Logout</a>
                    {% else %}
                        <a href="{{ url_for('auth.login') }}" class="block nav-link">Login</a>
                        <a href="{{ url_for('auth.register') }}" class="block nav-link">Sign Up</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mx-auto px-4 mt-4">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white mt-16">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4 text-gradient">Digital Nashop</h3>
                    <p class="text-gray-400">Your trusted marketplace for premium digital products including gift cards, social media followers, and gaming tokens.</p>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="{{ url_for('main.products') }}" class="text-gray-400 hover:text-white">All Products</a></li>
                        <li><a href="{{ url_for('main.products', category='giftcard') }}" class="text-gray-400 hover:text-white">Gift Cards</a></li>
                        <li><a href="{{ url_for('main.products', category='followers') }}" class="text-gray-400 hover:text-white">Social Media</a></li>
                        <li><a href="{{ url_for('main.products', category='gaming_tokens') }}" class="text-gray-400 hover:text-white">Gaming Tokens</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="{{ url_for('main.contact') }}" class="text-gray-400 hover:text-white">Contact Us</a></li>
                        <li><a href="{{ url_for('main.about') }}" class="text-gray-400 hover:text-white">About</a></li>
                        <li><a href="{{ url_for('main.terms') }}" class="text-gray-400 hover:text-white">Terms of Service</a></li>
                        <li><a href="{{ url_for('main.privacy') }}" class="text-gray-400 hover:text-white">Privacy Policy</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Connect</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Digital Nashop. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    <!-- Mobile menu toggle -->
    <script>
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
