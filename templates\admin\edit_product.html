{% extends "base.html" %}

{% block title %}Edit Product - Digital Nashop Admin{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Edit Product</h1>
                <p class="text-gray-600">Update product information</p>
            </div>
            <a href="{{ url_for('admin.products') }}" class="btn btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Products
            </a>
        </div>

        <!-- Edit Product Form -->
        <div class="glass-card p-8">
            <form method="POST">
                {{ csrf_token() }}
                
                <!-- Product Name -->
                <div class="form-group">
                    <label for="name" class="form-label">Product Name *</label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           class="form-input" 
                           value="{{ product.name }}"
                           required>
                </div>

                <!-- Category -->
                <div class="form-group">
                    <label for="category" class="form-label">Category *</label>
                    <select id="category" name="category" class="form-select" required>
                        <option value="giftcard" {% if product.category == 'giftcard' %}selected{% endif %}>Gift Cards</option>
                        <option value="followers" {% if product.category == 'followers' %}selected{% endif %}>Social Media Followers</option>
                        <option value="gaming_tokens" {% if product.category == 'gaming_tokens' %}selected{% endif %}>Gaming Tokens</option>
                    </select>
                </div>

                <!-- Description -->
                <div class="form-group">
                    <label for="description" class="form-label">Description</label>
                    <textarea id="description" 
                              name="description" 
                              rows="4" 
                              class="form-input">{{ product.description or '' }}</textarea>
                </div>

                <!-- Price -->
                <div class="form-group">
                    <label for="price" class="form-label">Price (USD) *</label>
                    <div class="relative">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input type="number" 
                               id="price" 
                               name="price" 
                               class="form-input pl-8" 
                               value="{{ product.price }}"
                               min="0.01" 
                               step="0.01"
                               required>
                    </div>
                </div>

                <!-- Stock -->
                <div class="form-group">
                    <label for="stock" class="form-label">Stock Quantity *</label>
                    <input type="number" 
                           id="stock" 
                           name="stock" 
                           class="form-input" 
                           value="{{ product.stock }}"
                           min="0"
                           required>
                </div>

                <!-- Image URL -->
                <div class="form-group">
                    <label for="image_url" class="form-label">Product Image URL</label>
                    <input type="url" 
                           id="image_url" 
                           name="image_url" 
                           class="form-input" 
                           value="{{ product.image_url or '' }}">
                    
                    <!-- Current Image Preview -->
                    {% if product.image_url %}
                    <div class="mt-4">
                        <img src="{{ product.image_url }}" 
                             alt="{{ product.name }}" 
                             class="w-32 h-32 object-cover rounded-lg border">
                    </div>
                    {% endif %}
                </div>

                <!-- Product Status -->
                <div class="form-group">
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="is_active" 
                               name="is_active" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                               {% if product.is_active %}checked{% endif %}>
                        <label for="is_active" class="ml-2 block text-sm text-gray-900">
                            Product is active
                        </label>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t">
                    <a href="{{ url_for('admin.products') }}" class="btn btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Update Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
