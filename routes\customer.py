from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import login_required, current_user
from models import Product, Order, Transaction, User, db
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

customer_bp = Blueprint('customer', __name__)

@customer_bp.route('/dashboard')
@login_required
def dashboard():
    """Customer dashboard"""
    if current_user.is_admin():
        return redirect(url_for('admin.dashboard'))
    
    # Get user statistics
    total_orders = Order.query.filter_by(user_id=current_user.id).count()
    pending_orders = Order.query.filter_by(user_id=current_user.id, status='pending').count()
    recent_orders = Order.query.filter_by(user_id=current_user.id).order_by(Order.created_at.desc()).limit(5).all()
    
    return render_template('customer/dashboard.html', 
                         total_orders=total_orders,
                         pending_orders=pending_orders,
                         recent_orders=recent_orders)

@customer_bp.route('/cart')
@login_required
def cart():
    """Shopping cart page"""
    cart_items = session.get('cart', {})
    cart_products = []
    total = 0
    
    for product_id, quantity in cart_items.items():
        product = Product.query.get(int(product_id))
        if product and product.is_active:
            item_total = float(product.price) * quantity
            cart_products.append({
                'product': product,
                'quantity': quantity,
                'total': item_total
            })
            total += item_total
    
    return render_template('customer/cart.html', cart_products=cart_products, total=total)

@customer_bp.route('/add-to-cart', methods=['POST'])
@login_required
def add_to_cart():
    """Add product to cart"""
    product_id = request.form.get('product_id', type=int)
    quantity = request.form.get('quantity', 1, type=int)
    
    if not product_id or quantity < 1:
        return jsonify({'success': False, 'message': 'Invalid product or quantity'})
    
    product = Product.query.get(product_id)
    if not product or not product.is_active:
        return jsonify({'success': False, 'message': 'Product not found'})
    
    if not product.is_in_stock(quantity):
        return jsonify({'success': False, 'message': 'Insufficient stock'})
    
    # Initialize cart if it doesn't exist
    if 'cart' not in session:
        session['cart'] = {}
    
    # Add or update cart item
    cart = session['cart']
    product_id_str = str(product_id)
    
    if product_id_str in cart:
        new_quantity = cart[product_id_str] + quantity
        if product.is_in_stock(new_quantity):
            cart[product_id_str] = new_quantity
        else:
            return jsonify({'success': False, 'message': 'Not enough stock available'})
    else:
        cart[product_id_str] = quantity
    
    session['cart'] = cart
    session.modified = True
    
    # Calculate cart total
    cart_count = sum(cart.values())
    
    return jsonify({
        'success': True, 
        'message': 'Product added to cart',
        'cart_count': cart_count
    })

@customer_bp.route('/update-cart', methods=['POST'])
@login_required
def update_cart():
    """Update cart item quantity"""
    product_id = request.form.get('product_id', type=int)
    quantity = request.form.get('quantity', 0, type=int)
    
    if 'cart' not in session:
        session['cart'] = {}
    
    cart = session['cart']
    product_id_str = str(product_id)
    
    if quantity <= 0:
        # Remove item from cart
        if product_id_str in cart:
            del cart[product_id_str]
    else:
        # Update quantity
        product = Product.query.get(product_id)
        if product and product.is_in_stock(quantity):
            cart[product_id_str] = quantity
        else:
            flash('Not enough stock available', 'error')
            return redirect(url_for('customer.cart'))
    
    session['cart'] = cart
    session.modified = True
    
    return redirect(url_for('customer.cart'))

@customer_bp.route('/checkout')
@login_required
def checkout():
    """Checkout page"""
    cart_items = session.get('cart', {})
    
    if not cart_items:
        flash('Your cart is empty', 'info')
        return redirect(url_for('main.products'))
    
    cart_products = []
    total = 0
    
    for product_id, quantity in cart_items.items():
        product = Product.query.get(int(product_id))
        if product and product.is_active and product.is_in_stock(quantity):
            item_total = float(product.price) * quantity
            cart_products.append({
                'product': product,
                'quantity': quantity,
                'total': item_total
            })
            total += item_total
        else:
            # Remove unavailable items from cart
            del cart_items[product_id]
            session.modified = True
    
    if not cart_products:
        flash('Some items in your cart are no longer available', 'warning')
        return redirect(url_for('customer.cart'))
    
    return render_template('customer/checkout.html', cart_products=cart_products, total=total)

@customer_bp.route('/place-order', methods=['POST'])
@login_required
def place_order():
    """Process order placement"""
    cart_items = session.get('cart', {})
    
    if not cart_items:
        flash('Your cart is empty', 'error')
        return redirect(url_for('main.products'))
    
    payment_method = request.form.get('payment_method')
    
    if payment_method not in ['wallet', 'card']:
        flash('Invalid payment method', 'error')
        return redirect(url_for('customer.checkout'))
    
    # Calculate total and validate cart
    total = 0
    orders_to_create = []
    
    for product_id, quantity in cart_items.items():
        product = Product.query.get(int(product_id))
        if not product or not product.is_active or not product.is_in_stock(quantity):
            flash('Some items in your cart are no longer available', 'error')
            return redirect(url_for('customer.cart'))
        
        item_total = float(product.price) * quantity
        total += item_total
        orders_to_create.append({
            'product': product,
            'quantity': quantity,
            'total': item_total
        })
    
    # Check payment method
    if payment_method == 'wallet':
        if float(current_user.balance) < total:
            flash('Insufficient wallet balance', 'error')
            return redirect(url_for('customer.checkout'))
    
    try:
        # Create orders and process payment
        for order_data in orders_to_create:
            # Create order
            order = Order(
                user_id=current_user.id,
                product_id=order_data['product'].id,
                quantity=order_data['quantity'],
                total_price=order_data['total'],
                status='pending'
            )
            db.session.add(order)
            db.session.flush()  # Get order ID
            
            # Reduce product stock
            order_data['product'].reduce_stock(order_data['quantity'])
            
            # Process payment
            if payment_method == 'wallet':
                current_user.deduct_from_balance(order_data['total'])
                
                # Create transaction record
                transaction = Transaction(
                    user_id=current_user.id,
                    order_id=order.id,
                    type='purchase',
                    amount=order_data['total'],
                    description=f"Purchase of {order_data['product'].name}"
                )
                db.session.add(transaction)
            
            # Update order status
            order.status = 'processing'
        
        db.session.commit()
        
        # Clear cart
        session['cart'] = {}
        session.modified = True
        
        flash('Order placed successfully!', 'success')
        return redirect(url_for('customer.orders'))
        
    except Exception as e:
        db.session.rollback()
        flash('Order processing failed. Please try again.', 'error')
        return redirect(url_for('customer.checkout'))

@customer_bp.route('/orders')
@login_required
def orders():
    """Order history page"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '')

    query = Order.query.filter_by(user_id=current_user.id)

    if status_filter and status_filter in ['pending', 'processing', 'delivered', 'cancelled']:
        query = query.filter_by(status=status_filter)

    orders = query.order_by(Order.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )

    return render_template('customer/orders.html', orders=orders, status_filter=status_filter)

@customer_bp.route('/wallet')
@login_required
def wallet():
    """Wallet management page"""
    page = request.args.get('page', 1, type=int)

    transactions = Transaction.query.filter_by(user_id=current_user.id).order_by(
        Transaction.created_at.desc()
    ).paginate(page=page, per_page=15, error_out=False)

    return render_template('customer/wallet.html', transactions=transactions)

@customer_bp.route('/add-funds', methods=['POST'])
@login_required
def add_funds():
    """Add funds to wallet (simulated)"""
    amount = request.form.get('amount', type=float)

    if not amount or amount <= 0 or amount > 1000:
        flash('Invalid amount. Please enter a value between $0.01 and $1000.', 'error')
        return redirect(url_for('customer.wallet'))

    try:
        # Add funds to user balance
        current_user.add_to_balance(amount)

        # Create transaction record
        transaction = Transaction(
            user_id=current_user.id,
            type='wallet_credit',
            amount=amount,
            description='Wallet credit (simulated payment)'
        )
        db.session.add(transaction)
        db.session.commit()

        flash(f'${amount:.2f} added to your wallet successfully!', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Failed to add funds. Please try again.', 'error')

    return redirect(url_for('customer.wallet'))

@customer_bp.route('/profile')
@login_required
def profile():
    """User profile page"""
    return render_template('customer/profile.html')

@customer_bp.route('/update-profile', methods=['POST'])
@login_required
def update_profile():
    """Update user profile"""
    username = request.form.get('username', '').strip()
    email = request.form.get('email', '').strip().lower()
    current_password = request.form.get('current_password', '')
    new_password = request.form.get('new_password', '')
    confirm_password = request.form.get('confirm_password', '')

    errors = []

    # Validate username
    if username != current_user.username:
        if len(username) < 3:
            errors.append('Username must be at least 3 characters long')
        elif User.query.filter_by(username=username).first():
            errors.append('Username already exists')

    # Validate email
    if email != current_user.email:
        if not email or '@' not in email:
            errors.append('Please enter a valid email address')
        elif User.query.filter_by(email=email).first():
            errors.append('Email already registered')

    # Validate password change
    if new_password:
        if not current_password:
            errors.append('Current password is required to change password')
        elif not check_password_hash(current_user.password_hash, current_password):
            errors.append('Current password is incorrect')
        elif len(new_password) < 6:
            errors.append('New password must be at least 6 characters long')
        elif new_password != confirm_password:
            errors.append('New passwords do not match')

    if errors:
        for error in errors:
            flash(error, 'error')
        return redirect(url_for('customer.profile'))

    try:
        # Update profile
        current_user.username = username
        current_user.email = email

        if new_password:
            current_user.password_hash = generate_password_hash(new_password)

        db.session.commit()
        flash('Profile updated successfully!', 'success')

    except Exception as e:
        db.session.rollback()
        flash('Failed to update profile. Please try again.', 'error')

    return redirect(url_for('customer.profile'))

@customer_bp.route('/cart-count')
@login_required
def cart_count():
    """Get cart item count for AJAX"""
    cart = session.get('cart', {})
    count = sum(cart.values())
    return jsonify({'count': count})
