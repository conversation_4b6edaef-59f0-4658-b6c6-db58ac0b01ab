{% extends "base.html" %}

{% block title %}Order Management - Digital Nashop Admin{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Order Management</h1>
            <p class="text-gray-600">Monitor and manage customer orders</p>
        </div>
    </div>

    <!-- Filters -->
    <div class="glass-card p-4 mb-6">
        <div class="flex flex-wrap items-center gap-4">
            <!-- Search -->
            <div class="flex-1 min-w-64">
                <input type="text" 
                       id="user-search"
                       class="form-input" 
                       placeholder="Search by username..."
                       value="{{ user_search }}">
            </div>

            <!-- Status Filter -->
            <select id="status-filter" class="form-select">
                <option value="">All Status</option>
                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                <option value="processing" {% if status_filter == 'processing' %}selected{% endif %}>Processing</option>
                <option value="delivered" {% if status_filter == 'delivered' %}selected{% endif %}>Delivered</option>
                <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>Cancelled</option>
            </select>
        </div>
    </div>

    <!-- Orders Table -->
    {% if orders.items %}
    <div class="glass-card overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for order in orders.items %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 text-sm font-medium text-gray-900">#{{ order.id }}</td>
                        <td class="px-6 py-4 text-sm text-gray-900">{{ order.user.username }}</td>
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <img src="{{ order.product.image_url or '/static/images/placeholder.jpg' }}" 
                                     alt="{{ order.product.name }}" 
                                     class="w-10 h-10 object-cover rounded mr-3">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ order.product.name }}</div>
                                    <div class="text-sm text-gray-500">Qty: {{ order.quantity }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">${{ "%.2f"|format(order.total_price) }}</td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif order.status == 'processing' %}bg-blue-100 text-blue-800
                                {% elif order.status == 'delivered' %}bg-green-100 text-green-800
                                {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                {% endif %}">
                                {{ order.status.title() }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500">{{ order.created_at.strftime('%b %d, %Y') }}</td>
                        <td class="px-6 py-4 text-right">
                            <form method="POST" action="{{ url_for('admin.update_order_status', order_id=order.id) }}" class="inline">
                                {{ csrf_token() }}
                                <select name="status" class="form-select text-sm" onchange="this.form.submit()">
                                    <option value="pending" {% if order.status == 'pending' %}selected{% endif %}>Pending</option>
                                    <option value="processing" {% if order.status == 'processing' %}selected{% endif %}>Processing</option>
                                    <option value="delivered" {% if order.status == 'delivered' %}selected{% endif %}>Delivered</option>
                                    <option value="cancelled" {% if order.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                                </select>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if orders.pages > 1 %}
        <div class="bg-white px-4 py-3 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing {{ ((orders.page - 1) * orders.per_page) + 1 }} to 
                        {{ orders.page * orders.per_page if orders.page * orders.per_page < orders.total else orders.total }} 
                        of {{ orders.total }} results
                    </p>
                </div>
                <div>
                    <nav class="flex items-center space-x-2">
                        {% if orders.has_prev %}
                            <a href="{{ url_for('admin.orders', page=orders.prev_num, status=status_filter, user=user_search) }}" 
                               class="btn btn-secondary btn-sm">Previous</a>
                        {% endif %}
                        
                        {% for page_num in orders.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != orders.page %}
                                    <a href="{{ url_for('admin.orders', page=page_num, status=status_filter, user=user_search) }}" 
                                       class="btn btn-secondary btn-sm">{{ page_num }}</a>
                                {% else %}
                                    <span class="btn btn-primary btn-sm">{{ page_num }}</span>
                                {% endif %}
                            {% else %}
                                <span class="px-3 py-1">…</span>
                            {% endif %}
                        {% endfor %}
                        
                        {% if orders.has_next %}
                            <a href="{{ url_for('admin.orders', page=orders.next_num, status=status_filter, user=user_search) }}" 
                               class="btn btn-secondary btn-sm">Next</a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% else %}
    <!-- No Orders -->
    <div class="glass-card p-12 text-center">
        <svg class="w-24 h-24 mx-auto text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
        </svg>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">No orders found</h2>
        <p class="text-gray-600">No orders match your current filters.</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const userSearch = document.getElementById('user-search');
    const statusFilter = document.getElementById('status-filter');
    
    let searchTimeout;
    userSearch.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            updateFilters();
        }, 500);
    });
    
    statusFilter.addEventListener('change', updateFilters);
    
    function updateFilters() {
        const url = new URL(window.location);
        
        if (userSearch.value) {
            url.searchParams.set('user', userSearch.value);
        } else {
            url.searchParams.delete('user');
        }
        
        if (statusFilter.value) {
            url.searchParams.set('status', statusFilter.value);
        } else {
            url.searchParams.delete('status');
        }
        
        url.searchParams.delete('page');
        window.location.href = url.toString();
    }
});
</script>
{% endblock %}
