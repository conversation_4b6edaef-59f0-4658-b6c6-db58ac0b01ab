#!/usr/bin/env python3
"""
Database Initialization Script for Digital Nashop
This script creates the database tables and sets up the initial admin user.
"""

from app import app
from models import db, User
from werkzeug.security import generate_password_hash

def init_database():
    """Initialize the database and create tables"""
    print("Initializing database...")
    
    with app.app_context():
        # Create all tables
        db.create_all()
        print("Database tables created successfully!")
        
        # Check if admin user already exists
        admin_user = User.query.filter_by(username='admin').first()
        
        if not admin_user:
            # Create admin user
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin',
                balance=0.00
            )
            
            db.session.add(admin)
            db.session.commit()
            
            print("Admin user created successfully!")
            print("Username: admin")
            print("Password: admin123")
        else:
            print("Admin user already exists!")
        
        print("\nDatabase initialization completed!")
        print("You can now run the application with: python app.py")
        print("Or populate with sample data using: python populate_sample_data.py")

if __name__ == '__main__':
    init_database()
