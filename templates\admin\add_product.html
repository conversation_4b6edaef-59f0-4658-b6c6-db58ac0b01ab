{% extends "base.html" %}

{% block title %}Add Product - Digital Nashop Admin{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Add New Product</h1>
                <p class="text-gray-600">Create a new digital product for your store</p>
            </div>
            <a href="{{ url_for('admin.products') }}" class="btn btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Products
            </a>
        </div>

        <!-- Add Product Form -->
        <div class="glass-card p-8">
            <form method="POST" id="add-product-form">
                {{ csrf_token() }}
                
                <!-- Product Name -->
                <div class="form-group">
                    <label for="name" class="form-label">Product Name *</label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           class="form-input" 
                           placeholder="Enter product name"
                           required>
                    <p class="text-sm text-gray-500 mt-1">Choose a clear, descriptive name for your product</p>
                </div>

                <!-- Category -->
                <div class="form-group">
                    <label for="category" class="form-label">Category *</label>
                    <select id="category" name="category" class="form-select" required>
                        <option value="">Select a category</option>
                        <option value="giftcard">Gift Cards</option>
                        <option value="followers">Social Media Followers</option>
                        <option value="gaming_tokens">Gaming Tokens</option>
                    </select>
                </div>

                <!-- Description -->
                <div class="form-group">
                    <label for="description" class="form-label">Description</label>
                    <textarea id="description" 
                              name="description" 
                              rows="4" 
                              class="form-input" 
                              placeholder="Describe your product features and benefits"></textarea>
                    <p class="text-sm text-gray-500 mt-1">Provide detailed information about what customers will receive</p>
                </div>

                <!-- Price -->
                <div class="form-group">
                    <label for="price" class="form-label">Price (USD) *</label>
                    <div class="relative">
                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                        <input type="number" 
                               id="price" 
                               name="price" 
                               class="form-input pl-8" 
                               placeholder="0.00"
                               min="0.01" 
                               step="0.01"
                               required>
                    </div>
                </div>

                <!-- Stock -->
                <div class="form-group">
                    <label for="stock" class="form-label">Stock Quantity *</label>
                    <input type="number" 
                           id="stock" 
                           name="stock" 
                           class="form-input" 
                           placeholder="0"
                           min="0"
                           required>
                    <p class="text-sm text-gray-500 mt-1">Number of units available for sale</p>
                </div>

                <!-- Image URL -->
                <div class="form-group">
                    <label for="image_url" class="form-label">Product Image URL</label>
                    <input type="url" 
                           id="image_url" 
                           name="image_url" 
                           class="form-input" 
                           placeholder="https://example.com/image.jpg">
                    <p class="text-sm text-gray-500 mt-1">Optional: Provide a URL to an image for your product</p>
                    
                    <!-- Image Preview -->
                    <div id="image-preview" class="mt-4 hidden">
                        <img id="preview-img" src="" alt="Product preview" class="w-32 h-32 object-cover rounded-lg border">
                    </div>
                </div>

                <!-- Product Status -->
                <div class="form-group">
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="is_active" 
                               name="is_active" 
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                               checked>
                        <label for="is_active" class="ml-2 block text-sm text-gray-900">
                            Make product active immediately
                        </label>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">Active products will be visible to customers</p>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t">
                    <a href="{{ url_for('admin.products') }}" class="btn btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Create Product
                    </button>
                </div>
            </form>
        </div>

        <!-- Tips -->
        <div class="glass-card p-6 mt-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">💡 Product Creation Tips</h3>
            <ul class="space-y-2 text-sm text-gray-600">
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Use clear, descriptive product names that customers can easily understand
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Include detailed descriptions explaining what customers will receive
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Set competitive prices based on market research
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Use high-quality product images to increase conversion rates
                </li>
                <li class="flex items-start">
                    <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Monitor stock levels regularly to avoid overselling
                </li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('add-product-form');
    const imageUrlInput = document.getElementById('image_url');
    const imagePreview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');
    
    // Image preview functionality
    imageUrlInput.addEventListener('input', function() {
        const url = this.value.trim();
        if (url) {
            previewImg.src = url;
            previewImg.onload = function() {
                imagePreview.classList.remove('hidden');
            };
            previewImg.onerror = function() {
                imagePreview.classList.add('hidden');
            };
        } else {
            imagePreview.classList.add('hidden');
        }
    });
    
    // Form validation
    form.addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const category = document.getElementById('category').value;
        const price = parseFloat(document.getElementById('price').value);
        const stock = parseInt(document.getElementById('stock').value);
        
        let errors = [];
        
        if (!name || name.length < 3) {
            errors.push('Product name must be at least 3 characters long');
        }
        
        if (!category) {
            errors.push('Please select a category');
        }
        
        if (!price || price <= 0) {
            errors.push('Price must be greater than 0');
        }
        
        if (stock === null || stock < 0) {
            errors.push('Stock must be 0 or greater');
        }
        
        if (errors.length > 0) {
            e.preventDefault();
            errors.forEach(error => {
                toast.show(error, 'error');
            });
            return;
        }
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <span class="spinner mr-2"></span>
            Creating Product...
        `;
    });
    
    // Auto-generate product name suggestions based on category
    const categorySelect = document.getElementById('category');
    const nameInput = document.getElementById('name');
    
    categorySelect.addEventListener('change', function() {
        if (!nameInput.value) {
            const suggestions = {
                'giftcard': 'Premium Gift Card',
                'followers': 'Social Media Followers Package',
                'gaming_tokens': 'Gaming Tokens Bundle'
            };
            
            if (suggestions[this.value]) {
                nameInput.placeholder = `e.g., ${suggestions[this.value]}`;
            }
        }
    });
    
    // Price formatting
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('blur', function() {
        if (this.value) {
            this.value = parseFloat(this.value).toFixed(2);
        }
    });
});
</script>
{% endblock %}
