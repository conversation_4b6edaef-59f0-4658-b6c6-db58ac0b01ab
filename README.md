# Digital Nashop - Premium Digital Products Marketplace

A modern, iOS-inspired e-commerce platform for digital products including gift cards, social media followers, and gaming tokens.

## Features

### Customer Features
- **Modern UI/UX**: iOS-inspired design with glassmorphism effects
- **Product Browsing**: Search, filter, and browse digital products by category
- **Shopping Cart**: Add products to cart with quantity management
- **Digital Wallet**: Secure wallet system for payments
- **Order Management**: Track order history and status
- **User Profile**: Manage account settings and preferences
- **Responsive Design**: Works perfectly on desktop and mobile devices

### Admin Features
- **Dashboard**: Comprehensive analytics and overview
- **Product Management**: Add, edit, and manage digital products
- **Order Management**: Process and track customer orders
- **User Management**: Manage customer accounts and permissions
- **Transaction Logs**: Monitor all financial transactions
- **Real-time Updates**: Live data updates and notifications

### Technical Features
- **Secure Authentication**: Flask-Login with password hashing
- **CSRF Protection**: Built-in security against cross-site attacks
- **Database Management**: SQLAlchemy ORM with SQLite
- **Session Management**: Secure shopping cart and user sessions
- **Form Validation**: Client-side and server-side validation
- **Error Handling**: Comprehensive error handling and user feedback

## Technology Stack

- **Backend**: Python Flask
- **Database**: SQLAlchemy with SQLite
- **Frontend**: HTML5, CSS3 (Tailwind CSS), JavaScript
- **Authentication**: Flask-Login
- **Security**: Flask-WTF (CSRF protection)
- **UI Framework**: Custom CSS with iOS-inspired design

## Installation & Setup

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Quick Start

1. **Clone or download the project files**

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Initialize the database**
   ```bash
   python init_db.py
   ```

4. **Populate with sample data (optional)**
   ```bash
   python populate_sample_data.py
   ```

5. **Run the application**
   ```bash
   python app.py
   ```

6. **Access the application**
   - Open your browser and go to: `http://localhost:5000`
   - Admin login: `admin` / `admin123`
   - Customer accounts (if sample data loaded): `john_doe` / `password123`

## Project Structure

```
digital-nashop/
├── app.py                 # Main Flask application
├── models.py              # Database models
├── init_db.py            # Database initialization script
├── populate_sample_data.py # Sample data population script
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── routes/               # Route handlers
│   ├── __init__.py
│   ├── auth.py          # Authentication routes
│   ├── main.py          # Public routes
│   ├── customer.py      # Customer dashboard routes
│   └── admin.py         # Admin panel routes
├── templates/            # HTML templates
│   ├── base.html        # Base template
│   ├── auth/            # Authentication templates
│   ├── main/            # Public page templates
│   ├── customer/        # Customer dashboard templates
│   └── admin/           # Admin panel templates
└── static/              # Static files
    ├── css/
    │   └── style.css    # Main stylesheet
    ├── js/
    │   └── main.js      # Main JavaScript
    └── images/          # Image assets
```

## Default Accounts

### Admin Account
- **Username**: `admin`
- **Password**: `admin123`
- **Access**: Full admin panel access

### Sample Customer Accounts (if sample data loaded)
- **Username**: `john_doe`, **Password**: `password123`
- **Username**: `jane_smith`, **Password**: `password123`
- **Username**: `mike_wilson`, **Password**: `password123`

## Usage Guide

### For Customers
1. **Browse Products**: Visit the homepage and browse by category
2. **Add to Cart**: Click "Add to Cart" on any product
3. **Checkout**: Review your cart and proceed to checkout
4. **Payment**: Use wallet balance or simulated card payment
5. **Track Orders**: View order status in your dashboard

### For Administrators
1. **Access Admin Panel**: Login with admin credentials
2. **Manage Products**: Add, edit, or remove products
3. **Process Orders**: Update order statuses and manage fulfillment
4. **Monitor Users**: View and manage customer accounts
5. **View Analytics**: Check sales data and transaction logs

## Configuration

The application uses the following default configuration:
- **Database**: SQLite (`digital_nashop.db`)
- **Secret Key**: Auto-generated for security
- **Debug Mode**: Enabled for development
- **Host**: `0.0.0.0` (accessible from network)
- **Port**: `5000`

## Security Features

- **Password Hashing**: Werkzeug security for password protection
- **CSRF Protection**: Flask-WTF prevents cross-site request forgery
- **Session Security**: Secure session management
- **Input Validation**: Server-side and client-side validation
- **SQL Injection Prevention**: SQLAlchemy ORM protection

## Demo Mode

This application includes demo/simulation features:
- **Simulated Payments**: No real payment processing
- **Sample Data**: Pre-populated products and orders
- **Test Environment**: Safe for testing and demonstration

## Customization

### Adding New Product Categories
1. Update the `category` choices in models and forms
2. Add category-specific styling in CSS
3. Update templates to handle new categories

### Modifying the Design
- Edit `static/css/style.css` for styling changes
- Modify templates in the `templates/` directory
- Update JavaScript in `static/js/main.js`

### Database Changes
1. Modify models in `models.py`
2. Delete the existing database file
3. Run `python init_db.py` to recreate tables

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`

2. **Database Errors**
   - Delete `digital_nashop.db` and run `python init_db.py`

3. **Port Already in Use**
   - Change the port in `app.py` or stop other applications using port 5000

4. **Template Not Found**
   - Ensure all template files are in the correct directories

### Getting Help

If you encounter issues:
1. Check the console output for error messages
2. Verify all files are in the correct locations
3. Ensure Python and pip are properly installed
4. Try recreating the database with `python init_db.py`

## License

This project is for educational and demonstration purposes.

## Contributing

This is a demonstration project. Feel free to fork and modify for your own use.
