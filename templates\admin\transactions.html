{% extends "base.html" %}

{% block title %}Transaction Logs - Digital Nashop Admin{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Transaction Logs</h1>
            <p class="text-gray-600">Monitor all financial transactions</p>
        </div>
    </div>

    <!-- Filters -->
    <div class="glass-card p-4 mb-6">
        <div class="flex flex-wrap items-center gap-4">
            <!-- Search -->
            <div class="flex-1 min-w-64">
                <input type="text" 
                       id="user-search"
                       class="form-input" 
                       placeholder="Search by username..."
                       value="{{ user_search }}">
            </div>

            <!-- Type Filter -->
            <select id="type-filter" class="form-select">
                <option value="">All Types</option>
                <option value="purchase" {% if type_filter == 'purchase' %}selected{% endif %}>Purchases</option>
                <option value="refund" {% if type_filter == 'refund' %}selected{% endif %}>Refunds</option>
                <option value="wallet_credit" {% if type_filter == 'wallet_credit' %}selected{% endif %}>Wallet Credits</option>
            </select>
        </div>
    </div>

    <!-- Transactions Table -->
    {% if transactions.items %}
    <div class="glass-card overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for transaction in transactions.items %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 text-sm font-medium text-gray-900">#{{ transaction.id }}</td>
                        <td class="px-6 py-4 text-sm text-gray-900">{{ transaction.user.username }}</td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if transaction.type == 'purchase' %}bg-red-100 text-red-800
                                {% elif transaction.type == 'refund' %}bg-green-100 text-green-800
                                {% elif transaction.type == 'wallet_credit' %}bg-blue-100 text-blue-800
                                {% endif %}">
                                {{ transaction.type.replace('_', ' ').title() }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm font-medium
                            {% if transaction.type == 'purchase' %}text-red-600
                            {% elif transaction.type in ['refund', 'wallet_credit'] %}text-green-600
                            {% endif %}">
                            {% if transaction.type == 'purchase' %}-{% else %}+{% endif %}${{ "%.2f"|format(transaction.amount) }}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">{{ transaction.description or 'No description' }}</td>
                        <td class="px-6 py-4 text-sm text-gray-500">{{ transaction.created_at.strftime('%b %d, %Y %I:%M %p') }}</td>
                        <td class="px-6 py-4 text-sm text-gray-500">
                            {% if transaction.order_id %}
                                <a href="{{ url_for('admin.orders') }}" class="text-blue-600 hover:text-blue-900">
                                    #{{ transaction.order_id }}
                                </a>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if transactions.pages > 1 %}
        <div class="bg-white px-4 py-3 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing {{ ((transactions.page - 1) * transactions.per_page) + 1 }} to 
                        {{ transactions.page * transactions.per_page if transactions.page * transactions.per_page < transactions.total else transactions.total }} 
                        of {{ transactions.total }} results
                    </p>
                </div>
                <div>
                    <nav class="flex items-center space-x-2">
                        {% if transactions.has_prev %}
                            <a href="{{ url_for('admin.transactions', page=transactions.prev_num, type=type_filter, user=user_search) }}" 
                               class="btn btn-secondary btn-sm">Previous</a>
                        {% endif %}
                        
                        {% for page_num in transactions.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != transactions.page %}
                                    <a href="{{ url_for('admin.transactions', page=page_num, type=type_filter, user=user_search) }}" 
                                       class="btn btn-secondary btn-sm">{{ page_num }}</a>
                                {% else %}
                                    <span class="btn btn-primary btn-sm">{{ page_num }}</span>
                                {% endif %}
                            {% else %}
                                <span class="px-3 py-1">…</span>
                            {% endif %}
                        {% endfor %}
                        
                        {% if transactions.has_next %}
                            <a href="{{ url_for('admin.transactions', page=transactions.next_num, type=type_filter, user=user_search) }}" 
                               class="btn btn-secondary btn-sm">Next</a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    {% else %}
    <!-- No Transactions -->
    <div class="glass-card p-12 text-center">
        <svg class="w-24 h-24 mx-auto text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
        </svg>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">No transactions found</h2>
        <p class="text-gray-600">No transactions match your current filters.</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const userSearch = document.getElementById('user-search');
    const typeFilter = document.getElementById('type-filter');
    
    let searchTimeout;
    userSearch.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            updateFilters();
        }, 500);
    });
    
    typeFilter.addEventListener('change', updateFilters);
    
    function updateFilters() {
        const url = new URL(window.location);
        
        if (userSearch.value) {
            url.searchParams.set('user', userSearch.value);
        } else {
            url.searchParams.delete('user');
        }
        
        if (typeFilter.value) {
            url.searchParams.set('type', typeFilter.value);
        } else {
            url.searchParams.delete('type');
        }
        
        url.searchParams.delete('page');
        window.location.href = url.toString();
    }
});
</script>
{% endblock %}
