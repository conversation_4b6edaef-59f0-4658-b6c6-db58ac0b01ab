{% extends "base.html" %}

{% block title %}Wallet - Digital Nashop{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Wallet Header -->
        <div class="glass-card p-8 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Digital Wallet</h1>
                    <p class="text-gray-600">Manage your wallet balance and transaction history</p>
                </div>
                
                <div class="text-right">
                    <div class="text-sm text-gray-500 mb-1">Current Balance</div>
                    <div class="text-4xl font-bold text-green-600">
                        ${{ "%.2f"|format(current_user.balance) }}
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Add Funds Section -->
            <div class="lg:col-span-1">
                <div class="glass-card p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">Add Funds</h2>
                    
                    <form method="POST" action="{{ url_for('customer.add_funds') }}" id="add-funds-form">
                        {{ csrf_token() }}
                        
                        <div class="form-group">
                            <label for="amount" class="form-label">Amount to Add</label>
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                                <input type="number" 
                                       id="amount" 
                                       name="amount" 
                                       class="form-input pl-8" 
                                       placeholder="0.00"
                                       min="0.01" 
                                       max="1000" 
                                       step="0.01"
                                       required>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Minimum: $0.01, Maximum: $1,000.00</p>
                        </div>

                        <!-- Quick Amount Buttons -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Quick Add</label>
                            <div class="grid grid-cols-2 gap-2">
                                <button type="button" onclick="setAmount(10)" class="btn btn-secondary btn-sm">$10</button>
                                <button type="button" onclick="setAmount(25)" class="btn btn-secondary btn-sm">$25</button>
                                <button type="button" onclick="setAmount(50)" class="btn btn-secondary btn-sm">$50</button>
                                <button type="button" onclick="setAmount(100)" class="btn btn-secondary btn-sm">$100</button>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                            <div class="space-y-2">
                                <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="payment_method" value="card" class="mr-3" checked>
                                    <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                    <span class="font-medium">Credit/Debit Card</span>
                                </label>
                                
                                <label class="flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="payment_method" value="paypal" class="mr-3">
                                    <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    <span class="font-medium">PayPal</span>
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-full">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Funds
                        </button>
                    </form>

                    <!-- Demo Notice -->
                    <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <p class="text-sm text-yellow-800">
                            <strong>Demo Mode:</strong> This is a simulated payment system. No real charges will be made.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Transaction History -->
            <div class="lg:col-span-2">
                <div class="glass-card p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900">Transaction History</h2>
                        <div class="text-sm text-gray-500">
                            Showing {{ transactions.items|length }} of {{ transactions.total }} transactions
                        </div>
                    </div>

                    {% if transactions.items %}
                        <div class="space-y-4">
                            {% for transaction in transactions.items %}
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="flex items-center space-x-4">
                                    <!-- Transaction Icon -->
                                    <div class="w-10 h-10 rounded-full flex items-center justify-center
                                        {% if transaction.type == 'purchase' %}bg-red-100
                                        {% elif transaction.type == 'refund' %}bg-green-100
                                        {% elif transaction.type == 'wallet_credit' %}bg-blue-100
                                        {% endif %}">
                                        {% if transaction.type == 'purchase' %}
                                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                            </svg>
                                        {% elif transaction.type == 'refund' %}
                                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                            </svg>
                                        {% elif transaction.type == 'wallet_credit' %}
                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                        {% endif %}
                                    </div>
                                    
                                    <!-- Transaction Details -->
                                    <div>
                                        <h3 class="font-semibold text-gray-900">
                                            {% if transaction.type == 'purchase' %}Purchase
                                            {% elif transaction.type == 'refund' %}Refund
                                            {% elif transaction.type == 'wallet_credit' %}Wallet Credit
                                            {% endif %}
                                        </h3>
                                        <p class="text-sm text-gray-600">{{ transaction.description or 'No description' }}</p>
                                        <p class="text-xs text-gray-500">
                                            {{ transaction.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                        </p>
                                        {% if transaction.order_id %}
                                            <p class="text-xs text-blue-600">
                                                <a href="{{ url_for('customer.orders') }}" class="hover:underline">
                                                    Order #{{ transaction.order_id }}
                                                </a>
                                            </p>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <!-- Transaction Amount -->
                                <div class="text-right">
                                    <span class="text-lg font-bold
                                        {% if transaction.type == 'purchase' %}text-red-600
                                        {% elif transaction.type in ['refund', 'wallet_credit'] %}text-green-600
                                        {% endif %}">
                                        {% if transaction.type == 'purchase' %}-{% else %}+{% endif %}${{ "%.2f"|format(transaction.amount) }}
                                    </span>
                                    <div class="text-xs text-gray-500">
                                        {% if transaction.type == 'purchase' %}Debit
                                        {% elif transaction.type in ['refund', 'wallet_credit'] %}Credit
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- Pagination -->
                        {% if transactions.pages > 1 %}
                        <div class="flex justify-center mt-6">
                            <nav class="flex items-center space-x-2">
                                {% if transactions.has_prev %}
                                    <a href="{{ url_for('customer.wallet', page=transactions.prev_num) }}" 
                                       class="btn btn-secondary btn-sm">
                                        Previous
                                    </a>
                                {% endif %}
                                
                                {% for page_num in transactions.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != transactions.page %}
                                            <a href="{{ url_for('customer.wallet', page=page_num) }}" 
                                               class="btn btn-secondary btn-sm">
                                                {{ page_num }}
                                            </a>
                                        {% else %}
                                            <span class="btn btn-primary btn-sm">{{ page_num }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="px-3 py-1">…</span>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if transactions.has_next %}
                                    <a href="{{ url_for('customer.wallet', page=transactions.next_num) }}" 
                                       class="btn btn-secondary btn-sm">
                                        Next
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                        {% endif %}
                    {% else %}
                        <!-- No Transactions -->
                        <div class="text-center py-8">
                            <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No transactions yet</h3>
                            <p class="text-gray-600">Your transaction history will appear here once you start using your wallet.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function setAmount(amount) {
    document.getElementById('amount').value = amount.toFixed(2);
}

document.addEventListener('DOMContentLoaded', function() {
    const addFundsForm = document.getElementById('add-funds-form');
    const amountInput = document.getElementById('amount');

    // Format amount input
    amountInput.addEventListener('input', function() {
        let value = parseFloat(this.value);
        if (value > 1000) {
            this.value = 1000;
            toast.show('Maximum amount is $1,000', 'warning');
        }
    });

    // Form submission
    addFundsForm.addEventListener('submit', function(e) {
        const amount = parseFloat(amountInput.value);
        
        if (!amount || amount <= 0) {
            e.preventDefault();
            toast.show('Please enter a valid amount', 'error');
            return;
        }
        
        if (amount > 1000) {
            e.preventDefault();
            toast.show('Maximum amount is $1,000', 'error');
            return;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <span class="spinner mr-2"></span>
            Processing...
        `;
    });
});
</script>
{% endblock %}
