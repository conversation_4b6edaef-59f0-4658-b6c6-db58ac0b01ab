#!/usr/bin/env python3

print("Starting test...")

try:
    print("Importing Flask...")
    from flask import Flask
    print("Flask imported successfully")
    
    print("Importing models...")
    from models import db, User
    print("Models imported successfully")
    
    print("Creating app...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-key'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    print("Initializing db...")
    db.init_app(app)
    
    print("Creating tables...")
    with app.app_context():
        db.create_all()
        print("Tables created successfully")
    
    print("Starting Flask app...")
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
