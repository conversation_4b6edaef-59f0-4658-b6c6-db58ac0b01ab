{% extends "base.html" %}

{% block title %}Dashboard - Digital Nashop{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Welcome Section -->
    <div class="glass-card p-8 mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    Welcome back, {{ current_user.username }}!
                </h1>
                <p class="text-gray-600">
                    Manage your orders, wallet, and account settings from your dashboard.
                </p>
            </div>
            <div class="text-right">
                <div class="text-sm text-gray-500">Wallet Balance</div>
                <div class="text-2xl font-bold text-green-600">
                    ${{ "%.2f"|format(current_user.balance) }}
                </div>
                <a href="{{ url_for('customer.wallet') }}" class="text-blue-600 hover:text-blue-800 text-sm">
                    Add Funds
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Total Orders -->
        <div class="glass-card p-6 text-center">
            <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-1">{{ total_orders }}</h3>
            <p class="text-gray-600">Total Orders</p>
            <a href="{{ url_for('customer.orders') }}" class="text-blue-600 hover:text-blue-800 text-sm">
                View All →
            </a>
        </div>

        <!-- Pending Orders -->
        <div class="glass-card p-6 text-center">
            <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-1">{{ pending_orders }}</h3>
            <p class="text-gray-600">Pending Orders</p>
            {% if pending_orders > 0 %}
                <a href="{{ url_for('customer.orders', status='pending') }}" class="text-yellow-600 hover:text-yellow-800 text-sm">
                    View Pending →
                </a>
            {% else %}
                <span class="text-gray-400 text-sm">All caught up!</span>
            {% endif %}
        </div>

        <!-- Account Status -->
        <div class="glass-card p-6 text-center">
            <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-bold text-gray-900 mb-1">Active</h3>
            <p class="text-gray-600">Account Status</p>
            <a href="{{ url_for('customer.profile') }}" class="text-green-600 hover:text-green-800 text-sm">
                Manage Profile →
            </a>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="glass-card p-6 mb-8">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="{{ url_for('main.products') }}" class="btn btn-primary text-center">
                <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                Shop Products
            </a>
            
            <a href="{{ url_for('customer.cart') }}" class="btn btn-secondary text-center">
                <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
                </svg>
                View Cart
            </a>
            
            <a href="{{ url_for('customer.wallet') }}" class="btn btn-secondary text-center">
                <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                Manage Wallet
            </a>
            
            <a href="{{ url_for('customer.profile') }}" class="btn btn-secondary text-center">
                <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Settings
            </a>
        </div>
    </div>

    <!-- Recent Orders -->
    {% if recent_orders %}
    <div class="glass-card p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-gray-900">Recent Orders</h2>
            <a href="{{ url_for('customer.orders') }}" class="text-blue-600 hover:text-blue-800">
                View All Orders →
            </a>
        </div>

        <div class="space-y-4">
            {% for order in recent_orders %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <img src="{{ order.product.image_url or '/static/images/placeholder.jpg' }}" 
                             alt="{{ order.product.name }}" 
                             class="w-12 h-12 object-cover rounded">
                        <div>
                            <h3 class="font-semibold text-gray-900">{{ order.product.name }}</h3>
                            <p class="text-sm text-gray-600">
                                Quantity: {{ order.quantity }} • 
                                Total: ${{ "%.2f"|format(order.total_price) }}
                            </p>
                            <p class="text-xs text-gray-500">
                                {{ order.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                            {% elif order.status == 'processing' %}bg-blue-100 text-blue-800
                            {% elif order.status == 'delivered' %}bg-green-100 text-green-800
                            {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                            {% endif %}">
                            {{ order.status.title() }}
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <div class="glass-card p-8 text-center">
        <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
        </svg>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">No Orders Yet</h3>
        <p class="text-gray-600 mb-4">Start shopping to see your orders here!</p>
        <a href="{{ url_for('main.products') }}" class="btn btn-primary">
            Browse Products
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
