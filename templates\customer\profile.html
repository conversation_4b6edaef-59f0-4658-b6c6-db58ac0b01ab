{% extends "base.html" %}

{% block title %}Profile Settings - Digital Nashop{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Profile Settings</h1>

        <!-- Profile Information -->
        <div class="glass-card p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Account Information</h2>
            
            <form method="POST" action="{{ url_for('customer.update_profile') }}" id="profile-form">
                {{ csrf_token() }}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Username -->
                    <div class="form-group">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" 
                               id="username" 
                               name="username" 
                               class="form-input" 
                               value="{{ current_user.username }}"
                               minlength="3"
                               required>
                        <div class="username-feedback text-sm mt-1" style="display: none;"></div>
                    </div>

                    <!-- Email -->
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               class="form-input" 
                               value="{{ current_user.email }}"
                               required>
                        <div class="email-feedback text-sm mt-1" style="display: none;"></div>
                    </div>
                </div>

                <!-- Account Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6 p-4 bg-gray-50 rounded-lg">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ current_user.role.title() }}</div>
                        <div class="text-sm text-gray-600">Account Type</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">${{ "%.2f"|format(current_user.balance) }}</div>
                        <div class="text-sm text-gray-600">Wallet Balance</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">{{ current_user.created_at.strftime('%b %Y') }}</div>
                        <div class="text-sm text-gray-600">Member Since</div>
                    </div>
                </div>

                <div class="flex justify-end mt-6">
                    <button type="submit" class="btn btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Update Profile
                    </button>
                </div>
            </form>
        </div>

        <!-- Change Password -->
        <div class="glass-card p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Change Password</h2>
            
            <form method="POST" action="{{ url_for('customer.update_profile') }}" id="password-form">
                {{ csrf_token() }}
                
                <!-- Hidden fields to maintain other profile data -->
                <input type="hidden" name="username" value="{{ current_user.username }}">
                <input type="hidden" name="email" value="{{ current_user.email }}">
                
                <div class="space-y-4">
                    <!-- Current Password -->
                    <div class="form-group">
                        <label for="current_password" class="form-label">Current Password</label>
                        <div class="relative">
                            <input type="password" 
                                   id="current_password" 
                                   name="current_password" 
                                   class="form-input pr-10" 
                                   placeholder="Enter your current password">
                            <button type="button" 
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                    onclick="togglePassword('current_password')">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- New Password -->
                    <div class="form-group">
                        <label for="new_password" class="form-label">New Password</label>
                        <div class="relative">
                            <input type="password" 
                                   id="new_password" 
                                   name="new_password" 
                                   class="form-input pr-10" 
                                   placeholder="Enter your new password"
                                   minlength="6">
                            <button type="button" 
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                    onclick="togglePassword('new_password')">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                        
                        <!-- Password Requirements -->
                        <div class="password-requirements mt-2 text-xs text-gray-500" style="display: none;">
                            <div class="requirement" data-requirement="length">
                                <span class="indicator">○</span> At least 6 characters
                            </div>
                            <div class="requirement" data-requirement="letter">
                                <span class="indicator">○</span> Contains a letter
                            </div>
                            <div class="requirement" data-requirement="number">
                                <span class="indicator">○</span> Contains a number
                            </div>
                        </div>
                    </div>

                    <!-- Confirm New Password -->
                    <div class="form-group">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <div class="relative">
                            <input type="password" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   class="form-input pr-10" 
                                   placeholder="Confirm your new password">
                            <button type="button" 
                                    class="absolute inset-y-0 right-0 pr-3 flex items-center"
                                    onclick="togglePassword('confirm_password')">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="confirm-password-feedback text-sm mt-1" style="display: none;"></div>
                    </div>
                </div>

                <div class="flex justify-end mt-6">
                    <button type="submit" class="btn btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        Change Password
                    </button>
                </div>
            </form>
        </div>

        <!-- Account Actions -->
        <div class="glass-card p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Account Actions</h2>
            
            <div class="space-y-4">
                <!-- Download Data -->
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                        <h3 class="font-semibold text-gray-900">Download Your Data</h3>
                        <p class="text-sm text-gray-600">Get a copy of your account data and transaction history</p>
                    </div>
                    <button onclick="downloadData()" class="btn btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m6-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Download
                    </button>
                </div>

                <!-- Logout All Devices -->
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                        <h3 class="font-semibold text-gray-900">Logout All Devices</h3>
                        <p class="text-sm text-gray-600">Sign out from all devices for security</p>
                    </div>
                    <button onclick="logoutAllDevices()" class="btn btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                        </svg>
                        Logout All
                    </button>
                </div>

                <!-- Delete Account -->
                <div class="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                    <div>
                        <h3 class="font-semibold text-red-900">Delete Account</h3>
                        <p class="text-sm text-red-600">Permanently delete your account and all data</p>
                    </div>
                    <button onclick="deleteAccount()" class="btn btn-danger">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Delete Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
    field.setAttribute('type', type);
}

function downloadData() {
    toast.show('Preparing your data download...', 'info');
    // Simulate data preparation
    setTimeout(() => {
        toast.show('Data download started! Check your downloads folder.', 'success');
    }, 2000);
}

function logoutAllDevices() {
    if (confirm('Are you sure you want to logout from all devices? You will need to login again.')) {
        window.location.href = '{{ url_for("auth.logout") }}';
    }
}

function deleteAccount() {
    const confirmation = prompt('Type "DELETE" to confirm account deletion:');
    if (confirmation === 'DELETE') {
        if (confirm('This action cannot be undone. Are you absolutely sure?')) {
            toast.show('Account deletion is not implemented in this demo', 'warning');
        }
    }
}

// Real-time validation
document.addEventListener('DOMContentLoaded', function() {
    const usernameField = document.getElementById('username');
    const emailField = document.getElementById('email');
    const newPasswordField = document.getElementById('new_password');
    const confirmPasswordField = document.getElementById('confirm_password');
    
    let usernameTimeout, emailTimeout;
    const originalUsername = '{{ current_user.username }}';
    const originalEmail = '{{ current_user.email }}';

    // Username validation
    usernameField.addEventListener('input', function() {
        if (this.value === originalUsername) return;
        
        clearTimeout(usernameTimeout);
        usernameTimeout = setTimeout(() => {
            checkUsername(this.value);
        }, 500);
    });

    // Email validation
    emailField.addEventListener('input', function() {
        if (this.value === originalEmail) return;
        
        clearTimeout(emailTimeout);
        emailTimeout = setTimeout(() => {
            checkEmail(this.value);
        }, 500);
    });

    // Password strength
    newPasswordField.addEventListener('input', function() {
        if (this.value.length > 0) {
            document.querySelector('.password-requirements').style.display = 'block';
            checkPasswordStrength(this.value);
        } else {
            document.querySelector('.password-requirements').style.display = 'none';
        }
        checkPasswordMatch();
    });

    // Confirm password
    confirmPasswordField.addEventListener('input', checkPasswordMatch);

    async function checkUsername(username) {
        if (username.length < 3) return;
        
        try {
            const response = await fetch(`/auth/check-username?username=${encodeURIComponent(username)}`);
            const data = await response.json();
            showFieldFeedback('username', data.message, data.available);
        } catch (error) {
            console.error('Username check failed:', error);
        }
    }

    async function checkEmail(email) {
        if (!email.includes('@')) return;
        
        try {
            const response = await fetch(`/auth/check-email?email=${encodeURIComponent(email)}`);
            const data = await response.json();
            showFieldFeedback('email', data.message, data.available);
        } catch (error) {
            console.error('Email check failed:', error);
        }
    }

    function checkPasswordStrength(password) {
        const requirements = {
            length: password.length >= 6,
            letter: /[a-zA-Z]/.test(password),
            number: /[0-9]/.test(password)
        };

        // Update requirement indicators
        Object.keys(requirements).forEach(req => {
            const element = document.querySelector(`[data-requirement="${req}"]`);
            const indicator = element.querySelector('.indicator');
            if (requirements[req]) {
                indicator.textContent = '✓';
                indicator.style.color = 'green';
            } else {
                indicator.textContent = '○';
                indicator.style.color = 'gray';
            }
        });
    }

    function checkPasswordMatch() {
        const password = newPasswordField.value;
        const confirmPassword = confirmPasswordField.value;
        
        if (confirmPassword.length > 0) {
            const match = password === confirmPassword;
            showFieldFeedback('confirm_password', 
                match ? 'Passwords match' : 'Passwords do not match', 
                match);
        }
    }

    function showFieldFeedback(fieldName, message, isValid) {
        const field = document.getElementById(fieldName);
        const feedback = document.querySelector(`.${fieldName}-feedback`);
        
        if (feedback) {
            feedback.textContent = message;
            feedback.style.display = 'block';
            feedback.style.color = isValid ? 'green' : 'red';
            
            field.style.borderColor = isValid ? 'green' : 'red';
        }
    }
});
</script>
{% endblock %}
