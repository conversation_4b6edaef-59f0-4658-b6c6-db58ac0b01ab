{% extends "base.html" %}

{% block title %}Admin Dashboard - Digital Nashop{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p class="text-gray-600">Welcome back, {{ current_user.username }}. Here's what's happening with your store.</p>
        </div>
        <div class="flex space-x-4">
            <a href="{{ url_for('admin.products') }}" class="btn btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Product
            </a>
            <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Store
            </a>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Revenue -->
        <div class="glass-card p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p class="text-2xl font-bold text-gray-900">${{ "%.2f"|format(total_revenue) }}</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <span class="text-green-600 font-medium">+${{ "%.2f"|format(weekly_revenue) }}</span>
                    <span class="text-gray-500 ml-2">this week</span>
                </div>
            </div>
        </div>

        <!-- Total Orders -->
        <div class="glass-card p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Orders</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_orders }}</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <span class="text-yellow-600 font-medium">{{ pending_orders }} pending</span>
                    <span class="text-gray-500 ml-2">{{ processing_orders }} processing</span>
                </div>
            </div>
        </div>

        <!-- Total Users -->
        <div class="glass-card p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_users }}</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <span class="text-green-600 font-medium">+{{ new_users_week }}</span>
                    <span class="text-gray-500 ml-2">this week</span>
                </div>
            </div>
        </div>

        <!-- Products -->
        <div class="glass-card p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Products</p>
                    <p class="text-2xl font-bold text-gray-900">{{ active_products }}/{{ total_products }}</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    {% if low_stock_products > 0 %}
                        <span class="text-red-600 font-medium">{{ low_stock_products }} low stock</span>
                    {% else %}
                        <span class="text-green-600 font-medium">All stocked</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Orders -->
        <div class="glass-card p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">Recent Orders</h2>
                <a href="{{ url_for('admin.orders') }}" class="text-blue-600 hover:text-blue-800 text-sm">
                    View All →
                </a>
            </div>

            {% if recent_orders %}
                <div class="space-y-4">
                    {% for order in recent_orders %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <img src="{{ order.product.image_url or '/static/images/placeholder.jpg' }}" 
                                 alt="{{ order.product.name }}" 
                                 class="w-10 h-10 object-cover rounded">
                            <div>
                                <p class="font-semibold text-gray-900">{{ order.product.name }}</p>
                                <p class="text-sm text-gray-600">{{ order.user.username }}</p>
                                <p class="text-xs text-gray-500">{{ order.created_at.strftime('%b %d, %Y') }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">${{ "%.2f"|format(order.total_price) }}</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif order.status == 'processing' %}bg-blue-100 text-blue-800
                                {% elif order.status == 'delivered' %}bg-green-100 text-green-800
                                {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                {% endif %}">
                                {{ order.status.title() }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                    <p class="text-gray-500">No orders yet</p>
                </div>
            {% endif %}
        </div>

        <!-- Popular Products -->
        <div class="glass-card p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">Popular Products (30 days)</h2>
                <a href="{{ url_for('admin.products') }}" class="text-blue-600 hover:text-blue-800 text-sm">
                    Manage →
                </a>
            </div>

            {% if popular_products %}
                <div class="space-y-4">
                    {% for product in popular_products %}
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="font-semibold text-gray-900">{{ product.name }}</p>
                            <p class="text-sm text-gray-600">{{ product.order_count }} orders</p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-green-600">${{ "%.2f"|format(product.total_revenue) }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <p class="text-gray-500">No sales data yet</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="glass-card p-6 mt-8">
        <h2 class="text-xl font-bold text-gray-900 mb-6">Quick Actions</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="{{ url_for('admin.add_product') }}" class="btn btn-primary text-center">
                <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Product
            </a>
            
            <a href="{{ url_for('admin.orders', status='pending') }}" class="btn btn-secondary text-center">
                <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Pending Orders
            </a>
            
            <a href="{{ url_for('admin.users') }}" class="btn btn-secondary text-center">
                <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                Manage Users
            </a>
            
            <a href="{{ url_for('admin.transactions') }}" class="btn btn-secondary text-center">
                <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                View Transactions
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh dashboard data every 30 seconds
setInterval(function() {
    // In a real application, you might want to fetch updated data via AJAX
    // For now, we'll just show a subtle indicator that data is being refreshed
    const indicators = document.querySelectorAll('.glass-card');
    indicators.forEach(card => {
        card.style.opacity = '0.8';
        setTimeout(() => {
            card.style.opacity = '1';
        }, 200);
    });
}, 30000);

// Add some interactivity to metric cards
document.addEventListener('DOMContentLoaded', function() {
    const metricCards = document.querySelectorAll('.glass-card');
    metricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %}
