{% extends "base.html" %}

{% block title %}Checkout - Digital Nashop{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <!-- Progress Steps -->
        <div class="mb-8">
            <div class="flex items-center justify-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                    <span class="ml-2 text-sm font-medium text-gray-900">Cart</span>
                </div>
                <div class="w-16 h-1 bg-blue-600"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                    <span class="ml-2 text-sm font-medium text-blue-600">Checkout</span>
                </div>
                <div class="w-16 h-1 bg-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Complete</span>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Order Form -->
            <div class="glass-card p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Complete Your Order</h2>
                
                <form method="POST" action="{{ url_for('customer.place_order') }}" id="checkout-form">
                    {{ csrf_token() }}
                    
                    <!-- Payment Method -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Method</h3>
                        
                        <div class="space-y-3">
                            <!-- Wallet Payment -->
                            <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" 
                                       name="payment_method" 
                                       value="wallet" 
                                       class="mr-3"
                                       {% if current_user.balance >= total %}checked{% endif %}
                                       {% if current_user.balance < total %}disabled{% endif %}>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                            </svg>
                                            <div>
                                                <div class="font-semibold">Wallet Balance</div>
                                                <div class="text-sm text-gray-600">
                                                    Available: ${{ "%.2f"|format(current_user.balance) }}
                                                </div>
                                            </div>
                                        </div>
                                        {% if current_user.balance >= total %}
                                            <span class="text-green-600 text-sm font-semibold">✓ Sufficient</span>
                                        {% else %}
                                            <span class="text-red-600 text-sm font-semibold">Insufficient</span>
                                        {% endif %}
                                    </div>
                                    {% if current_user.balance < total %}
                                        <div class="mt-2">
                                            <a href="{{ url_for('customer.wallet') }}" 
                                               class="text-blue-600 hover:text-blue-800 text-sm underline">
                                                Add ${{ "%.2f"|format(total - current_user.balance) }} to wallet
                                            </a>
                                        </div>
                                    {% endif %}
                                </div>
                            </label>
                            
                            <!-- Credit Card Payment (Simulated) -->
                            <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input type="radio" 
                                       name="payment_method" 
                                       value="card" 
                                       class="mr-3"
                                       {% if current_user.balance < total %}checked{% endif %}>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                        <div>
                                            <div class="font-semibold">Credit/Debit Card</div>
                                            <div class="text-sm text-gray-600">Secure payment processing</div>
                                        </div>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Billing Information (for card payments) -->
                    <div id="billing-info" class="mb-6" style="display: none;">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Billing Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-input" placeholder="John">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-input" placeholder="Doe">
                            </div>
                            
                            <div class="form-group md:col-span-2">
                                <label class="form-label">Email Address</label>
                                <input type="email" class="form-input" value="{{ current_user.email }}" readonly>
                            </div>
                            
                            <div class="form-group md:col-span-2">
                                <label class="form-label">Card Number</label>
                                <input type="text" class="form-input" placeholder="1234 5678 9012 3456" maxlength="19">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Expiry Date</label>
                                <input type="text" class="form-input" placeholder="MM/YY" maxlength="5">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">CVV</label>
                                <input type="text" class="form-input" placeholder="123" maxlength="4">
                            </div>
                        </div>
                        
                        <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <p class="text-sm text-yellow-800">
                                <strong>Demo Mode:</strong> This is a simulated payment. No real charges will be made.
                            </p>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mb-6">
                        <label class="flex items-start">
                            <input type="checkbox" class="mt-1 mr-3" required>
                            <span class="text-sm text-gray-600">
                                I agree to the 
                                <a href="{{ url_for('main.terms') }}" class="text-blue-600 hover:text-blue-800" target="_blank">Terms of Service</a> 
                                and understand that digital products are delivered instantly and are non-refundable except in cases of technical issues.
                            </span>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit" class="btn btn-primary w-full btn-lg" id="place-order-btn">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        Place Order - ${{ "%.2f"|format(total) }}
                    </button>
                </form>
            </div>

            <!-- Order Summary -->
            <div class="glass-card p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Order Summary</h2>
                
                <!-- Order Items -->
                <div class="space-y-4 mb-6">
                    {% for item in cart_products %}
                    <div class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                        <img src="{{ item.product.image_url or '/static/images/placeholder.jpg' }}" 
                             alt="{{ item.product.name }}" 
                             class="w-12 h-12 object-cover rounded">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900">{{ item.product.name }}</h3>
                            <p class="text-sm text-gray-600">
                                Quantity: {{ item.quantity }} × ${{ "%.2f"|format(item.product.price) }}
                            </p>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold">${{ "%.2f"|format(item.total) }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Order Totals -->
                <div class="border-t pt-4 space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Subtotal</span>
                        <span>${{ "%.2f"|format(total) }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Processing Fee</span>
                        <span>$0.00</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">Taxes</span>
                        <span>$0.00</span>
                    </div>
                    
                    <div class="border-t pt-2">
                        <div class="flex justify-between text-lg font-bold">
                            <span>Total</span>
                            <span class="text-blue-600">${{ "%.2f"|format(total) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Security Features -->
                <div class="mt-6 p-4 bg-green-50 rounded-lg">
                    <h3 class="font-semibold text-green-800 mb-2">Secure Checkout</h3>
                    <ul class="text-sm text-green-700 space-y-1">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            SSL encrypted payment
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Instant digital delivery
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            24/7 customer support
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const billingInfo = document.getElementById('billing-info');
    const checkoutForm = document.getElementById('checkout-form');
    const placeOrderBtn = document.getElementById('place-order-btn');

    // Show/hide billing info based on payment method
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            if (this.value === 'card') {
                billingInfo.style.display = 'block';
            } else {
                billingInfo.style.display = 'none';
            }
        });
    });

    // Form submission handling
    checkoutForm.addEventListener('submit', function(e) {
        const selectedPayment = document.querySelector('input[name="payment_method"]:checked');
        
        if (!selectedPayment) {
            e.preventDefault();
            alert('Please select a payment method');
            return;
        }

        // Show loading state
        placeOrderBtn.disabled = true;
        placeOrderBtn.innerHTML = `
            <span class="spinner mr-2"></span>
            Processing Order...
        `;
    });

    // Format card number input
    const cardNumberInput = document.querySelector('input[placeholder="1234 5678 9012 3456"]');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function() {
            let value = this.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            this.value = formattedValue;
        });
    }

    // Format expiry date input
    const expiryInput = document.querySelector('input[placeholder="MM/YY"]');
    if (expiryInput) {
        expiryInput.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            this.value = value;
        });
    }
});
</script>
{% endblock %}
